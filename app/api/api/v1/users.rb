module Api
  module V1
    class Users < Grape::API
      before { authenticate_user! }

      resource :users do
        desc 'Update user details'
        params do
          optional :date_of_birth, type: Date, desc: 'Date of Birth'
          optional :gender, type: String, desc: 'Gender'
          optional :city, type: String, desc: 'City'
          optional :country, type: String, desc: 'Country'
          optional :height, type: Float, desc: 'Height in cm'
          optional :weight, type: Float, desc: 'Weight in kg'
        end
        put do
          user = current_user
          if user.update(declared(params, include_missing: false))
            present user, with: Entities::User
          else
            error!({ message: user.errors.full_messages.first }, 422)
          end
        end

        desc 'Check if user exists by email'
        params do
          requires :email, type: String
        end
        get :exists do
          user = User.find_by(email: params[:email])
          { exists: user.present?, id: user&.id }
        end
      end
    end
  end
end
