module Api
  module V1
    module Auth
      class Passwords < Grape::API
        resource :forgot_password do
          desc 'Send password reset instructions'
          params do
            requires :email, type: String, desc: 'Email'
          end

          post do
            user = User.find_by(email: params[:email])

            if user
              user.send_reset_password_instructions
              { message: 'Password reset instructions sent.' }
            else
              error!({ message: '<PERSON><PERSON> not found' }, 404)
            end
          end
        end
      end
    end
  end
end
