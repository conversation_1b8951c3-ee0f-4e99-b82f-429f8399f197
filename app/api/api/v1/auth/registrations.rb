module Api
  module V1
    module Auth
      class Registrations < Grape::API
        resource :register do
          desc 'Register a new user'
          params do
            optional :email, type: String, desc: 'Email'
            optional :first_name, type: String, desc: 'First Name'
            optional :last_name, type: String, desc: 'Last Name'
            optional :password, type: String, desc: 'Password'
            optional :password_confirmation, type: String, desc: 'Password Confirmation'
            optional :provider, type: String, desc: 'OAuth provider (google, facebook, apple)'
            optional :uid, type: String, desc: 'OAuth UID'
          end

          post do
            if params[:provider].present? && params[:uid].present?
              user = User.from_omniauth(params)
            else
              user = User.new(
                email: params[:email],
                password: params[:password],
                password_confirmation: params[:password_confirmation],
                first_name: params[:first_name],
                last_name: params[:last_name],
              )
            end

            if user.save
              token = JWT.encode({ user_id: user.id, exp: 24.hours.from_now.to_i }, ENV['JWT_SECRET'])
              { message: 'User registered successfully. Please confirm your email.', user:, token: }
            else
              error!({ message: user.errors.full_messages.first }, 422)
            end
          end
        end
      end
    end
  end
end
