# app/api/api/v1/auth/sessions.rb
module Api
  module V1
    module Auth
      class Sessions < Grape::API
        resource :login do
          desc 'Login user'
          params do
            requires :email, type: String, desc: 'Email'
            requires :password, type: String, desc: 'Password'
          end

          post do
            user = User.find_by(email: params[:email])

            if user
              if user.provider.present?
                error!({ message: "Use #{user.provider.capitalize} login." }, 401)
              elsif user.valid_password?(params[:password])
                token = JWT.encode({ user_id: user.id, exp: 24.hours.from_now.to_i }, ENV['JWT_SECRET'])
                { message: 'Login successful', token: token, user: user }
              else
                error!({ message: 'Invalid credentials' }, 401)
              end
            else
              status 404
              return { message: 'User not found' }
            end
          end
        end
      end
    end
  end
end
