module Api
  module V1
    module Auth
      class Confirmations < Grape::API
        resource :confirm_email do
          desc 'Confirm user email'
          params do
            requires :confirmation_token, type: String, desc: 'Confirmation Token'
          end

          get do
            user = User.confirm_by_token(params[:confirmation_token])

            if user.errors.empty?
              { message: '<PERSON><PERSON> confirmed successfully.' }
            else
              error!({ message: user.errors.full_messages }, 422)
            end
          end
        end
      end
    end
  end
end
