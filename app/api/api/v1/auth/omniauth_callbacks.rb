module Api
  module V1
    module Auth
      class OmniauthCallbacks < Grape::API
        resource :oauth_callback do
          desc 'Handle OAuth callback'
          params do
            requires :provider, type: String, desc: 'OAuth provider (google, facebook, apple)'
          end

          get ':provider/callback' do
            auth = request.env['omniauth.auth']
            user = User.from_omniauth(auth)

            if user.persisted?
              token = JWT.encode({ user_id: user.id, exp: 24.hours.from_now.to_i }, ENV['JWT_SECRET'])
              { message: "#{auth.provider.capitalize} login successful", token: token, user: user }
            else
              error!({ message: 'OAuth login failed' }, 401)
            end
          end
        end
      end
    end
  end
end
