module Api
  module V1
    class ShareRequests < Grape::API

      resource :share_requests do
        before { authenticate_user! }

        desc "Create a new share request"
        params do
          requires :recipient_email, type: String
        end
        post do
          recipient = User.find_by(email: params[:recipient_email].downcase)
          error!('Recipient not found', 404) unless recipient
          error!('Cannot share with yourself', 400) if recipient.id == current_user.id

          bottle = current_user.default_bottle
          error!('No bottle found', 422) unless bottle

          existing = ShareRequest.find_by(
            sender_id: current_user.id,
            recipient_id: recipient.id,
            bottle_id: bottle.id,
            status: 'pending'
          )
          error!('A pending request already exists', 409) if existing

          ShareRequest.create!(
            sender_id: current_user.id,
            recipient_id: recipient.id,
            bottle_id: bottle.id,
            status: 'pending',
            requested_at: Time.current
          )

          { message: 'Share request sent successfully' }
        end


        desc "List incoming and outgoing share requests"
        get do
          incoming = ShareRequest
            .where(recipient_id: current_user.id)
            .includes(:sender)
            .order(created_at: :desc)
            .map do |req|
              {
                id: req.id,
                sender_email: req.sender.email,
                bottle_id: req.bottle_id,
                access_level: req.access_level,
                status: req.status,
                requested_at: req.requested_at
              }
            end

          outgoing = ShareRequest
            .where(sender_id: current_user.id)
            .includes(:recipient)
            .order(created_at: :desc)
            .map do |req|
              {
                id: req.id,
                recipient_email: req.recipient.email,
                bottle_id: req.bottle_id,
                access_level: req.access_level,
                status: req.status,
                requested_at: req.requested_at
              }
            end
          puts "Incoming: #{incoming.inspect}"
          puts "Outgoing: #{outgoing.inspect}"
          { incoming: incoming, outgoing: outgoing }
        end

        params do
          requires :id, type: Integer
        end
        route_param :id do
          desc "Respond to a share request"
          params do
            requires :action, type: String, values: %w[accept reject]
            optional :access_level, type: String, values: %w[water tablet both]
          end
          patch :respond do
            share_request = ShareRequest.find(params[:id])

            error!('Unauthorized', 403) unless share_request.recipient_id == current_user.id
            error!('Already responded', 400) unless share_request.status == 'pending'

            if params[:action] == 'reject'
              share_request.update!(status: 'rejected', responded_at: Time.current)
              return { message: 'Share request rejected' }
            end

            error!('Access level required', 422) unless params[:access_level]

            AccessPermission.create!(
              bottle_id: share_request.bottle_id,
              granted_by_user_id: share_request.sender_id,
              granted_to_user_id: share_request.recipient_id,
              access_level: params[:access_level],
              granted_at: Time.current
            )

            share_request.update!(status: 'accepted', responded_at: Time.current)
            { message: 'Request accepted, access granted' }
          end
        end


      end
    end
  end
end
