module Api
  class Base < Grape::API
    prefix 'api'
    format :json
    version 'v1', using: :path
    helpers do 
      def authenticate_user!
        error!('401 Unauthorized', 401) unless current_user
      end

      def current_user
        # Check for the Authorization header
        token = headers['Authorization']&.split(' ')&.last
        return nil if token.nil?
        decoded_token = JWT.decode(token, ENV['JWT_SECRET'], true)
        user_id = decoded_token.first['user_id']
        @current_user ||= User.find(user_id)
      end
    end

    mount Api::V1::Auth::Base
    mount Api::V1::Users
    mount Api::V1::ShareRequests

    add_swagger_documentation(
      info: {
        title: 'Nutrition Bottle API',
        description: 'API documentation for the Nutrition Bottle project'
      },
      schemes: ['http'],
      base_path: '/',
      mount_path: '/swagger_doc',
      hide_format: true,
      hide_version: true
    )
  end
end
