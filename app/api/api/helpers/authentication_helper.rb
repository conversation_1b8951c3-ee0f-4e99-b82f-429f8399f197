module Api
  module Helpers
    module AuthenticationHelper
      def authenticate_user!
        error!('401 Unauthorized', 401) unless current_user
      end

      def current_user
        token = headers['Authorization']&.split(' ')&.last
        return nil if token.nil?

        begin
          decoded_token = JWT.decode(token, ENV['JWT_SECRET'], true, { algorithm: 'HS256' })
          user_id = decoded_token.first['user_id']
          @current_user ||= User.find(user_id)
        rescue JWT::DecodeError
          nil
        end
      end
    end
  end
end
