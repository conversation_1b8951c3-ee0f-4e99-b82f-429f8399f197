class User < ApplicationRecord
  OAUTH_PROVIDERS = %w[google facebook apple].freeze
  has_many :tablet_trackings, dependent: :destroy
  has_many :water_trackings, dependent: :destroy
  has_many :bottles, dependent: :destroy
  has_many :share_requests, dependent: :destroy
  has_many :access_permissions, dependent: :destroy
  devise :database_authenticatable, :registerable,
         :recoverable, :rememberable, :validatable, :omniauthable, :trackable, :confirmable

  validates :provider, inclusion: { in: OAUTH_PROVIDERS, allow_blank: true }
  validates :gender, inclusion: { in: %w[male female other], allow_blank: true }

  class << self 
    def from_omniauth(auth)
      where(provider: auth.provider, uid: auth.uid).first_or_initialize.tap do |user|
        user.email = auth.info.email if user.email.blank?
        user.first_name = auth.info.first_name || auth.info.name.split.first
        user.last_name = auth.info.last_name || auth.info.name.split.last
        user.password = Devise.friendly_token[0, 20] if user.new_record?
        user.skip_confirmation!
        user.save!
      end
    end
  end

  def oauth_user?
    provider.present?
  end

  def default_bottle
    bottles.first_or_create!
  end
end
