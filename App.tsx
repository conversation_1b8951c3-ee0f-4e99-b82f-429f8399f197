import React from 'react';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import AppNavigator from './src/navigation/AppNavigator';
import { Platform } from 'react-native';
import Gutter from './src/modules/shared/components/Gutter';

const App = () => {

  return (
    <SafeAreaProvider>
      {Platform.OS === 'ios' && <Gutter size={56} />}
      <AppNavigator />
    </SafeAreaProvider>
  );
};

export default App;