<%
  username = ENV["DATABASE_USER"] || "postgres"
  password = ENV["DATABASE_PASSWORD"]
  host     = ENV["DATABASE_HOST"] ||  "localhost"
  port     = ENV["DATABASE_PORT"] || "5432"
  auth = [username, password].compact_blank.join(":")
%>

default: &default
  schema_search_path: "\"$user\", public, event_log"

development:
  <<: *default
  url: <%= ENV.fetch("DATABASE_URL") { "postgresql://#{auth}@#{host}:#{port}/snb_development?pool=5&encoding=unicode" } %>

test:
  <<: *default
  url: <%= ENV.fetch("TEST_DATABASE_URL") { "postgresql://#{auth}@#{host}:#{port}/snb_test#{ENV['TEST_ENV_NUMBER']}?encoding=unicode" } %>

production:
  <<: *default
  url: <%= ENV.fetch("DATABASE_URL") { "postgresql://missing.example.com/" } %>
  encoding: unicode
  pool: <%= ENV["SIDEKIQ_DB_POOL"] || ENV["DB_POOL"] || ENV["MAX_THREADS"] || 5 %>
