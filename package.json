{"name": "smart_nutrition_bottle_app", "displayName": "Smart Nutrition Bottle App", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@nozbe/watermelondb": "^0.28.0", "@react-native-async-storage/async-storage": "^2.1.0", "@react-native-community/checkbox": "^0.5.20", "@react-native-community/datetimepicker": "^8.3.0", "@react-native-community/push-notification-ios": "^1.11.0", "@react-native-segmented-control/segmented-control": "^2.5.7", "@react-native-vector-icons/common": "^11.0.0", "@react-native-vector-icons/feather": "^4.29.2", "@react-native-vector-icons/ionicons": "^7.4.0", "@react-navigation/native": "^7.0.14", "@react-navigation/native-stack": "^7.2.0", "@types/react-native-push-notification": "^8.1.4", "axios": "^1.7.9", "buffer": "^6.0.3", "cbor-js": "^0.1.0", "date-fns": "^4.1.0", "lodash": "^4.17.21", "mobx": "^6.13.5", "mobx-react-lite": "^4.1.0", "moment": "^2.30.1", "moment-timezone": "^0.5.48", "react": "18.3.1", "react-native": "0.76.5", "react-native-autocomplete-input": "^5.5.6", "react-native-ble-manager": "^12.1.3", "react-native-calendars": "^1.1312.0", "react-native-circular-progress": "^1.4.1", "react-native-dotenv": "^3.4.11", "react-native-gesture-handler": "^2.21.2", "react-native-gifted-charts": "^1.4.57", "react-native-linear-gradient": "^2.8.3", "react-native-modal": "^13.0.1", "react-native-permissions": "^5.2.4", "react-native-push-notification": "^8.1.1", "react-native-reanimated": "^3.16.6", "react-native-reanimated-carousel": "^3.5.1", "react-native-safe-area-context": "^5.0.0", "react-native-screens": "^4.4.0", "react-native-size-matters": "^0.4.2", "react-native-svg": "^15.11.1", "react-native-uuid": "^2.0.3", "yarn": "^1.22.22"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/plugin-proposal-decorators": "^7.25.9", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "15.0.1", "@react-native-community/cli-platform-android": "15.0.1", "@react-native-community/cli-platform-ios": "15.0.1", "@react-native/babel-preset": "0.76.5", "@react-native/eslint-config": "0.76.5", "@react-native/metro-config": "0.76.5", "@react-native/typescript-config": "0.76.5", "@types/cbor-js": "^0.1.1", "@types/lodash": "^4.17.16", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "better-sqlite3": "^11.9.1", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "18.3.1", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}