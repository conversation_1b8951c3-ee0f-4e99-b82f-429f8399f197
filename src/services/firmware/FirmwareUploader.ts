import { Buffer } from 'buffer';
import { buildSMPMessage } from './SMPEncoder';
import Ble<PERSON>anager from 'react-native-ble-manager';
import * as Crypto from 'crypto';

interface UploadOptions {
  peripheralId: string;
  serviceUUID: string;
  characteristicUUID: string;
  firmwareData: ArrayBuffer;
  chunkSize?: number;
  onProgress?: (percent: number) => void;
}

export async function uploadFirmware(options: UploadOptions): Promise<void> {
  const {
    peripheralId,
    serviceUUID,
    characteristicUUID,
    firmwareData,
    chunkSize = 512,
    onProgress,
  } = options;

  const buffer = Buffer.from(firmwareData);
  const firmwareSize = buffer.length;
  const sha256 = Crypto.createHash('sha256').update(buffer).digest();
  let offset = 0;
  let sequence = 0;

  while (offset < firmwareSize) {
    const chunk = buffer.slice(offset, offset + chunkSize);
    const message = buildSMPMessage({
      offset,
      chunk,
      firmwareSize,
      sha256,
      sequence,
    });

    await BleManager.write(
      peripheralId,
      serviceUUID,
      characteristicUUID,
      Array.from(message),
    );

    // If needed, you could wait for and parse response here

    offset += chunk.length;
    sequence += 1;

    if (onProgress) {
      onProgress(Math.floor((offset / firmwareSize) * 100));
    }
  }
}
