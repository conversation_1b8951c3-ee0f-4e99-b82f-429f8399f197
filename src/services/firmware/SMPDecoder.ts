import cbor from 'cbor-js';

/**
 * Decodes a raw SMP response buffer from a BLE notification.
 * @param rawBytes The full SMP message as an array of numbers.
 * @returns Object with header info and decoded CBOR payload.
 */
export function decodeSMPResponse(rawBytes: number[]) {
  if (rawBytes.length < 8) {
    throw new Error('Invalid SMP response: too short');
  }

  // Parse SMP header
  const op = rawBytes[0];
  const flags = rawBytes[1];
  const len = (rawBytes[2] << 8) | rawBytes[3];
  const groupId = (rawBytes[4] << 8) | rawBytes[5];
  const sequence = rawBytes[6];
  const commandId = rawBytes[7];

  const header = {
    op,
    flags,
    len,
    groupId,
    sequence,
    commandId,
  };

  // Extract payload and decode via CBOR
  const payloadBytes = rawBytes.slice(8, 8 + len);
  const uint8Payload = new Uint8Array(payloadBytes);

  let decodedPayload = {};
  try {
    decodedPayload = cbor.decode(uint8Payload.buffer);
  } catch (err) {
    console.error('CBOR decode error:', err);
    throw new Error('Failed to decode CBOR payload');
  }

  return {
    header,
    payload: decodedPayload,
  };
}
