import cbor from 'cbor-js';
import {Buffer} from 'buffer';

export function buildSMPMessage(params: {
  offset: number;
  chunk: Buffer;
  firmwareSize: number;
  sha256: Buffer;
  sequence: number;
}): Buffer {
  const {offset, chunk, firmwareSize, sha256, sequence} = params;

  const payload = {
    off: offset,
    data: chunk.toString('base64'),
    len: firmwareSize,
    sha: sha256,
  };

  const encodedPayload = cbor.encode(payload);
  const length = encodedPayload.byteLength;

  const header = Buffer.alloc(8);
  header[0] = 0x01; // Write
  header[1] = 0x00; // Flags
  header.writeUInt16BE(length, 2); // Length
  header[4] = 0x00; // Group ID High
  header[5] = 0x01; // Group ID Low
  header[6] = sequence; // Sequence
  header[7] = 0x01; // Command ID

  return Buffer.concat([header, Buffer.from(new Uint8Array(encodedPayload))]);
}
