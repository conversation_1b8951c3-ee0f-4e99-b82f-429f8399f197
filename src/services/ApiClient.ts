import axios, {AxiosError} from 'axios';
import { ErrorResponse } from '../modules/shared/types/ErrorResponse';
import AsyncStorage from '@react-native-async-storage/async-storage';

const API_BASE_URL = 'http://10.0.2.2:3000/api/v1';

const ApiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    Accept: 'application/json',
  },
});

ApiClient.interceptors.request.use(
  async (config) => {
    const token = await AsyncStorage.getItem('token'); 
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    console.error('Request error:', error);
    return Promise.reject(error);
  }
);

ApiClient.interceptors.response.use(
  (response) => response,
  (error: AxiosError) => {
    console.log('Api Error:', error.response);
    
    const errorData = error.response?.data as { message?: string, error?: string } || {};
    const errorResponse: ErrorResponse = {
      message: errorData.message || errorData.error || 'Something went wrong, please try again later',
      data: errorData,
    };

    if (!error.response) {
      console.error('Request error:', error);
    }

    return Promise.reject(errorResponse);
  }
);


export default ApiClient;
