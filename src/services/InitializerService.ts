import {
  Alert,
  Linking,
  Platform,
} from 'react-native';
import {
  checkMultiple,
  requestMultiple,
  Permission,
  PERMISSIONS,
  RESULTS,
  canScheduleExactAlarms,
  openSettings
} from 'react-native-permissions';
import B<PERSON>Manager from 'react-native-ble-manager';
import { PermissionsAndroid } from 'react-native';

class InitializerService {
  public bluetoothPermissionGranted: boolean = false;
  public notificationPermissionGranted: boolean = false;
  public exactAlarmPermissionGranted: boolean = false;

  protected bluetoothPermissions: Permission[] =
    Platform.OS === 'android'
      ? [PERMISSIONS.ANDROID.BLUETOOTH_CONNECT, PERMISSIONS.ANDROID.BLUETOOTH_SCAN, PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION]
      : [PERMISSIONS.IOS.BLUETOOTH];

  private static instance: InitializerService;

  private constructor() { }

  static getInstance(): InitializerService {
    if (!this.instance) {
      this.instance = new InitializerService();
    }
    return this.instance;
  }

  public async init(): Promise<void> {
    await this.initPermissions();
    await this.initServices();
  }

  private async initPermissions(): Promise<void> {
    await this.checkBluetoothPermissions();
    await this.checkNotificationPermissions();
    await this.checkExactAlarmPermissions();
  }

  private async checkBluetoothPermissions() {
    const bluetoothResults = await checkMultiple(this.bluetoothPermissions);
    const bluetoothGranted = this.bluetoothPermissions.every(permission => bluetoothResults[permission] === RESULTS.GRANTED);

    if (bluetoothGranted) {
      this.bluetoothPermissionGranted = true;
    } else {
      await this.requestBluetoothPermissions();
    }
  }

  private async checkNotificationPermissions() {
    if (Platform.OS === 'android') {
      const hasPermission = await PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS);

      if (hasPermission) {
        this.notificationPermissionGranted = true;
      } else {
        await this.requestNotificationPermissions();
      }
    } else {
      this.notificationPermissionGranted = true; // iOS assumed granted
    }
  }

  private async checkExactAlarmPermissions() {
    if (Platform.OS === 'android' && Platform.Version >= 31) { // Android 12+
      const canSchedule = await canScheduleExactAlarms();
      console.log('canScheduleExactAlarms:', canSchedule);

      if (canSchedule === true) {
        this.exactAlarmPermissionGranted = true;
      } else if (!canSchedule) {
        this.exactAlarmPermissionGranted = false;
        this.showExactAlarmPermissionAlert();
      }
    } else {
      this.exactAlarmPermissionGranted = true; // Not required for Android < 12
    }
  }

  private async requestBluetoothPermissions() {
    const results = await requestMultiple(this.bluetoothPermissions);
    const granted = this.bluetoothPermissions.every(permission => results[permission] === RESULTS.GRANTED);

    if (granted) {
      this.bluetoothPermissionGranted = true;
    } else {
      this.bluetoothPermissionGranted = false;
      this.showBluetoothPermissionAlert();
    }
  }

  private async requestNotificationPermissions() {
    const result = await PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS);

    if (result === PermissionsAndroid.RESULTS.GRANTED) {
      this.notificationPermissionGranted = true;
    } else {
      this.notificationPermissionGranted = false;
      this.showNotificationPermissionAlert();
    }
  }

  private showBluetoothPermissionAlert() {
    Alert.alert(
      'Bluetooth Permission Required',
      'Please allow Bluetooth access to connect with the Bottle.',
      [
        {
          text: 'Grant permissions',
          onPress: () => Linking.openSettings().catch(console.error),
        },
        { text: 'Cancel', onPress: () => { }, style: 'cancel' },
      ]
    );
  }

  private showNotificationPermissionAlert() {
    Alert.alert(
      'Notification Permission Required',
      'Please allow the app to send you reminders.',
      [
        {
          text: 'Grant permissions',
          onPress: () => Linking.openSettings().catch(console.error),
        },
        { text: 'Cancel', onPress: () => { }, style: 'cancel' },
      ]
    );
  }

  private showExactAlarmPermissionAlert() {
    Alert.alert(
      'Exact Alarm Permission Required',
      'Please allow exact alarms so reminders can work correctly at the scheduled time.',
      [
        {
          text: 'Grant permissions',
          onPress: () => openSettings('alarms').catch(console.error),
        },
        { text: 'Cancel', onPress: () => { }, style: 'cancel' },
      ]
    );
  }

  private async initServices(): Promise<void> {
    if (this.bluetoothPermissionGranted && this.notificationPermissionGranted && this.exactAlarmPermissionGranted) {
      if (Platform.OS === 'android') {
        BLEManager.enableBluetooth()
          .then(() => {
            // Bluetooth enabled
          })
          .catch(() => {
            Alert.alert('Bluetooth not enabled', 'Please enable Bluetooth to use the app', [
              {
                text: 'OK',
                onPress: () => Linking.openSettings().catch(console.error),
              },
              { text: 'Cancel', onPress: () => { }, style: 'cancel' },
            ]);
          });
      } else {
        // iOS logic
      }
    }
  }
}

export default InitializerService;
