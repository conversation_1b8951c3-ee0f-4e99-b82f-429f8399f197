import database from ".";

// Seed data
async function seedDatabase() {
  // Check if the database is empty
  const userCollection = database.collections.get('users');
  const count = await userCollection.query().fetchCount();
  if (count > 0) {
    console.log('Database already seeded. Skipping seed.');
    return;
  }

  await database.write(async () => {
    const userCollection = database.collections.get('users');

    await userCollection.create((user: any) => {
      user.name = '<PERSON>';
      user.email = '<EMAIL>';
    });
  });

  console.log('Database seeded successfully!');
}

