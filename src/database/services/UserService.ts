import database from "..";
import User from "../models/User";
import { Q } from "@nozbe/watermelondb";
import { TableName } from "../schema";

export const createUser = async (data: Partial<User> & Pick<User, 'email' | 'first_name' | 'last_name'>) => {
  return await database.write(async () => {
    return await database.collections.get<User>(TableName.USERS).create(user => {
      user.first_name = data.first_name;
      user.last_name = data.last_name;
      user.email = data.email;
      user.gender = data.gender ?? '';
      user.mobile = data.mobile ?? '';
      user.height = data.height ?? 0;
      user.weight = data.weight ?? 0;
      user.token = data.token ?? '';
    });
  });
};

export const findUserByEmail = async (email: string) => {
  const user = await database.collections.get<User>(TableName.USERS).query(
    Q.where('email', email),
  ).fetch();
  return user[0] || null;
};

export const updateUser = async (data: Partial<User>) => {
  const user = await database.collections.get<User>(TableName.USERS).find(data.id!);
  await database.write(async () => {
    user.update(u => {
      u.gender = data.gender ?? '';
      u.mobile = data.mobile ?? '';
      u.height = data.height ?? 0;
      u.weight = data.weight ?? 0;
      u.token = data.token ?? '';
    });
  });
};