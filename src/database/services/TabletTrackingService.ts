import { Q } from "@nozbe/watermelondb";
import database from "..";
import TabletTracking from "../models/TabletTracking";
import { TableName } from "../schema";
import { isSameDay, startOfWeek, addDays } from 'date-fns'

export interface DayDoseStatus {
  data:
  {
    date: Date
    taken: boolean
  }[]
  todayTaken: boolean
}

export const getTabletTrackingForWeek = async (
  userId: string,
  cartridgeId: string
): Promise<DayDoseStatus> => {
  const today = new Date()

  const weekStart = startOfWeek(today, { weekStartsOn: 1 })
  const weekDays = Array.from({ length: 7 }, (_, i) => addDays(weekStart, i))

  const trackingCollection = database.collections.get<TabletTracking>(TableName.TABLET_TRACKINGS)
  const records = await trackingCollection
    .query(
      Q.where('user_id', userId),
      Q.where('cartridge_id', cartridgeId),
      Q.where('created_at', Q.gte(weekStart.getTime()))
    )
    .fetch()
  console.log('Records:', records.map(day => day.createdAt.toISOString().split('T')[0]));
  console.log('Week Days:', weekDays.map(day => day.toISOString().split('T')[0]));
  return {
    data: weekDays.map((day) => ({
      date: day,
      taken: records.some(record => isSameDay(record.createdAt.toISOString().split('T')[0], day)),
    })),
    todayTaken: records.some(record => isSameDay(new Date(record.createdAt), today)),
  }
}
