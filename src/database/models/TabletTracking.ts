import { Model, Q, Relation } from '@nozbe/watermelondb';
import { field, date, readonly, relation, writer } from '@nozbe/watermelondb/decorators';
import Cartridge from './Cartridge';
import User from './User';
import { TableName } from '../schema';
import { Associations } from '@nozbe/watermelondb/Model';

export default class TabletTracking extends Model {
  static table = TableName.TABLET_TRACKINGS;
  static associations: Associations = {
    [TableName.USERS]: { type: 'belongs_to', key: 'user_id' },
    [TableName.CARTRIDGES]: { type: 'belongs_to', key: 'cartridge_id' },
  };

  @relation(TableName.USERS, 'user_id') user!: Relation<User>;
  @relation(TableName.CARTRIDGES, 'cartridge_id') cartridge!: Relation<Cartridge>;
  @field('user_id') user_id!: string;
  @field('cartridge_id') cartridge_id!: string;
  @field('name') name!: string;
  @readonly @date('created_at') createdAt!: Date;
  @readonly @date('updated_at') updatedAt!: Date;

  static byName(name: string) {
    return Q.where('name', name)
  }

  static byUserId(id: string) {
    return Q.where('user_id', id)
  }
}
