import { Model, Relation } from '@nozbe/watermelondb';
import { field, date, readonly, relation, writer } from '@nozbe/watermelondb/decorators';
import User from './User';
import { TableName } from '../schema';
import { Associations } from '@nozbe/watermelondb/Model';

export default class WaterTracking extends Model {
  static table = TableName.WATER_TRACKINGS
  static associations: Associations = {
    [TableName.USERS]: { type: 'belongs_to', key: 'user_id' },
  };

  @relation(TableName.USERS, 'user_id') user!: Relation<User>;
  @field('user_id') user_id!: string;
  @field('consumed_ml') consumedMl!: number;
  @readonly @date('created_at') createdAt!: Date;
  @readonly @date('updated_at') updatedAt!: Date;
}
