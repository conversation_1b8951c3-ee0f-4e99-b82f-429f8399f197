import { Model, Relation } from '@nozbe/watermelondb';
import { field, date, readonly, writer, relation } from '@nozbe/watermelondb/decorators';
import User from './User';
import { TableName } from '../schema';
import { Associations } from '@nozbe/watermelondb/Model';
import TabletTracking from './TabletTracking';

export default class Cartridge extends Model {
  static table = TableName.CARTRIDGES;
  static associations: Associations = {
    [TableName.USERS]: { type: 'belongs_to', key: 'user_id' },
    [TableName.TABLET_TRACKINGS]: { type: 'has_many', foreignKey: 'cartridge_id' },
  }
  @relation(TableName.USERS, 'user_id') user!: Relation<User>;
  @field('user_id') user_id!: string
  @field('name') name!: string;
  @field('tablet_count') tablet_count!: number;
  @field('installed') installed!: boolean;
  @field('completed') completed!: boolean;
  @field('uuid') uuid!: string;
  @readonly @date('created_at') createdAt!: Date;
  @readonly @date('updated_at') updatedAt!: Date;

  @writer async markInstalled() {
    await this.update((cartridge) => {
      cartridge.installed = true;
    });
  }

  @writer async logTabletIntake(name: string) {
    await this.update((cartridge) => {
      cartridge.tablet_count -= 1;
    });
    const logged = await this.collections.get<TabletTracking>(TableName.TABLET_TRACKINGS).create(tabletTracking => {
      tabletTracking.user_id = this.user_id;
      tabletTracking.cartridge_id = this.id;
      tabletTracking.name = name;
    });
    return logged;
  }

}