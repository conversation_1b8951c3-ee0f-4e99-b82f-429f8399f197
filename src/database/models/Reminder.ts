import { Model, Relation } from '@nozbe/watermelondb';
import { field, date, readonly, relation } from '@nozbe/watermelondb/decorators';
import User from './User';
import { TableName } from '../schema';
import { Associations } from '@nozbe/watermelondb/Model';

export default class Reminder extends Model {
  static table = TableName.REMINDERS;
  static associations: Associations = {
    [TableName.USERS]: { type: 'belongs_to', key: 'user_id' },
  }

  @relation(TableName.USERS, 'user_id') user!: Relation<User>;
  @field('user_id') user_id!: string
  @field('reminder_type') reminderType!: string;
  @field('schedule') schedule!: string;
  @field('status') status!: string;
  @readonly @date('created_at') createdAt!: Date;
  @readonly @date('updated_at') updatedAt!: Date;
  @field('description') description?: string;
  @field('notification_id') notificationId?: number;
}
