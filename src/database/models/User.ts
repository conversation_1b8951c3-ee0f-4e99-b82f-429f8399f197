import { Model, Q, Query } from '@nozbe/watermelondb';
import { field, date, readonly, children, writer, reader } from '@nozbe/watermelondb/decorators';
import Cartridge from './Cartridge';
import uuid from 'react-native-uuid'
import { TableName } from '../schema';
import { Associations } from '@nozbe/watermelondb/Model';
import WaterTracking from './WaterTracking';
import TabletTracking from './TabletTracking';
import { endOfDay, startOfDay, subDays } from 'date-fns';
import Reminder from './Reminder';

export default class User extends Model {
  static table = TableName.USERS;
  static associations: Associations = {
    [TableName.CARTRIDGES]: { type: 'has_many', foreignKey: 'user_id' },
    [TableName.TABLET_TRACKINGS]: { type: 'has_many', foreignKey: 'user_id' },
    [TableName.WATER_TRACKINGS]: { type: 'has_many', foreignKey: 'user_id' },
    [TableName.REMINDERS]: { type: 'has_many', foreignKey: 'user_id' },
  }

  @children(TableName.CARTRIDGES) cartridges!: Query<Cartridge>;
  @children(TableName.TABLET_TRACKINGS) tablet_trackings!: Query<TabletTracking>;
  @children(TableName.WATER_TRACKINGS) water_trackings!: Query<WaterTracking>;
  @children(TableName.REMINDERS) reminders!: Query<Reminder>;
  @field('first_name') first_name!: string;
  @field('last_name') last_name!: string;
  @field('email') email!: string;
  @field('mobile') mobile!: string;
  @field('gender') gender!: string;
  @field('height') height!: number;
  @field('weight') weight!: number;
  @field('token') token!: string;
  @readonly @date('created_at') createdAt!: Date;
  @readonly @date('updated_at') updatedAt!: Date;

  @writer async createCartridges(name: string, quantity: number) {
    const cartridges = [];
    for (let i = 0; i < quantity; i++) {
      const cartridge = await this.collections.get<Cartridge>('cartridges').create((cartridge) => {
        cartridge.name = name;
        cartridge.tablet_count = 7;
        cartridge.installed = false;
        cartridge.completed = false;
        cartridge.user_id = this.id;
        cartridge.uuid = uuid.v4() as string;
      });
      cartridges.push(cartridge);
    }
    return cartridges;
  }

  @writer async updateUser(details: Partial<User>) {
    const user = await this.update((user) => {
      user.first_name = details.first_name || user.first_name;
      user.last_name = details.last_name || user.last_name;
      user.email = details.email || user.email;
      user.mobile = details.mobile || user.mobile;
      user.gender = details.gender || user.gender;
      user.height = details.height || user.height;
      user.weight = details.weight || user.weight;
    });
    return user;
  }

  @writer async fetchOrInstallCartridges() {
    const remainingCartridges = (await this.cartridges.fetch()).filter((cartridge) => !cartridge.completed);
    const installedCartridges = remainingCartridges.filter((cartridge) => cartridge.installed);
    const uninstalledCartridges = remainingCartridges.filter((cartridge) => !cartridge.installed);
    if (installedCartridges.length > 0) {
      return installedCartridges;
    } else {
      const cartridgesToInstall = uninstalledCartridges.slice(0, 2);
      for (const cartridge of cartridgesToInstall) {
        await cartridge.update((cartridge) => {
          cartridge.installed = true;
        }
        );
      }
      return cartridgesToInstall;
    }
  }

  @writer async logWaterIntake(consumedMl: number) {
    const newWaterTracking = await this.collections.get<WaterTracking>(TableName.WATER_TRACKINGS).create(waterTracking => {
      waterTracking.user_id = this.id;
      waterTracking.consumedMl = consumedMl;
    });
    return newWaterTracking;
  }

  @reader
  async getWaterIntake() {
    const todayStart = startOfDay(new Date()).getTime();
    const todayEnd = endOfDay(new Date()).getTime();

    const waterTrackings = await this.collections
      .get<WaterTracking>(TableName.WATER_TRACKINGS)
      .query(
        Q.where('created_at', Q.gte(todayStart)),
        Q.where('created_at', Q.lte(todayEnd))
      )
      .fetch();

    const totalWaterIntake = waterTrackings.reduce(
      (total, tracking) => total + tracking.consumedMl,
      0
    );

    return totalWaterIntake;
  }

  @reader
  async getWaterIntakeStreak(): Promise<number> {
    const waterTrackingCollection = this.collections.get<WaterTracking>(TableName.WATER_TRACKINGS);

    // Fetch all distinct days with water intake, ordered descending
    const allIntakes = await waterTrackingCollection
      .query(
        Q.sortBy('created_at', Q.desc)
      )
      .fetch();

    const uniqueDaysSet = new Set<string>();

    for (const tracking of allIntakes) {
      const day = startOfDay(new Date(tracking.createdAt)).toISOString();
      uniqueDaysSet.add(day);
    }

    const uniqueDays = Array.from(uniqueDaysSet).sort((a, b) => new Date(b).getTime() - new Date(a).getTime());

    // Compute streak starting from today going backwards
    let streak = 0;
    let currentDate = startOfDay(new Date());

    for (const dayStr of uniqueDays) {
      const day = new Date(dayStr);
      if (day.getTime() === currentDate.getTime()) {
        streak++;
        currentDate = subDays(currentDate, 1);
      } else {
        break;
      }
    }

    return streak;
  }

  @writer async createReminder(type: string, schedule: string, description: string) {
    const reminder = await this.collections.get<Reminder>(TableName.REMINDERS).create((reminder) => {
      reminder.user_id = this.id;
      reminder.reminderType = type;
      reminder.schedule = schedule;
      reminder.status = 'active';
      reminder.description = description;
      reminder.notificationId = Math.floor(Math.random() * (2 ** 31 - 1));
    });
    return reminder;
  }

  @writer async getDatesTracked(name: string) {
    const trackings = await this.collections
      .get<TabletTracking>(TableName.TABLET_TRACKINGS)
      .query(
        TabletTracking.byUserId(this.id),
        TabletTracking.byName(name)
      )
      .fetch()

    return trackings.map((tracking) => {
      return tracking.createdAt.toISOString().split('T')[0];
    });
  }
}
