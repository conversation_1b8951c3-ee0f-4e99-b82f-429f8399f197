import { schemaMigrations } from '@nozbe/watermelondb/Schema/migrations';
import { TableName } from './schema';

export const migrations = schemaMigrations({
  migrations: [
    {
      toVersion: 2,
      steps: [
        { type: 'add_columns', table: TableName.TABLET_TRACKINGS, columns: [
          { name: 'name', type: 'string', isOptional: true }
        ]}
      ]
    },
    {
      toVersion: 3,
      steps: [
        { type: 'add_columns', table: TableName.REMINDERS, columns: [
          { name: 'description', type: 'string', isOptional: true },
        ]}
      ]
    },
    {
      toVersion: 4,
      steps: [
        { type: 'add_columns', table: TableName.REMINDERS, columns: [
          { name: 'notification_id', type: 'number', isOptional: true },
        ]}
      ]
    },
  ],
});
