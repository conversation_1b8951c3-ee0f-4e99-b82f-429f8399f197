import { Database } from "@nozbe/watermelondb";
import Cartridge from "./models/Cartridge";
import Reminder from "./models/Reminder";
import SQLiteAdapter from '@nozbe/watermelondb/adapters/sqlite';
import schema from "./schema";
import User from "./models/User";
import TabletTracking from "./models/TabletTracking";
import WaterTracking from "./models/WaterTracking";
import { migrations } from "./migrations";

const adapter = new SQLiteAdapter({
  dbName: 'app',
  schema,
  migrations: migrations,
  jsi: true,
  onSetUpError: (error) => {
    console.error("Database setup error:", error);
  },
});

const database = new Database({
  adapter,
  modelClasses: [Cartridge, Reminder, User, TabletTracking, WaterTracking],
});

export default database;