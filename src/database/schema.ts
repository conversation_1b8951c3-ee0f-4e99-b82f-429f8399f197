import { appSchema, tableSchema } from '@nozbe/watermelondb';

export enum TableName {
  USERS = 'users',
  CARTRIDGES = 'cartridges',
  TABLET_TRACKINGS = 'tablet_trackings',
  WATER_TRACKINGS = 'water_trackings',
  REMINDERS = 'reminders',
}

const schema = appSchema({
  version: 4,
  tables: [
    tableSchema({
      name: 'users',
      columns: [
        { name: 'first_name', type: 'string' },
        { name: 'last_name', type: 'string' },
        { name: 'mobile', type: 'string' },
        { name: 'email', type: 'string' },
        { name: 'gender', type: 'string' },
        { name: 'height', type: 'number' },
        { name: 'weight', type: 'number' },
        { name: 'token', type: 'string' },
        { name: 'created_at', type: 'number' },
        { name: 'updated_at', type: 'number' },
      ]
    }),
    tableSchema({
      name: 'cartridges',
      columns: [
        { name: 'user_id', type: 'string' },
        { name: 'name', type: 'string' },
        { name: 'tablet_count', type: 'number' },
        { name: 'installed', type: 'boolean' },
        { name: 'completed', type: 'boolean' },
        { name: 'uuid', type: 'string' },
        { name: 'created_at', type: 'number' },
        { name: 'updated_at', type: 'number' },
      ],
    }),
    tableSchema({
      name: 'tablet_trackings',
      columns: [
        { name: 'user_id', type: 'string' },
        { name: 'cartridge_id', type: 'string' },
        { name: 'name', type: 'string' },
        { name: 'created_at', type: 'number' },
        { name: 'updated_at', type: 'number' },
      ],
    }),
    tableSchema({
      name: 'water_trackings',
      columns: [
        { name: 'user_id', type: 'string' },
        { name: 'consumed_ml', type: 'number' },
        { name: 'created_at', type: 'number' },
        { name: 'updated_at', type: 'number' },
      ],
    }),
    tableSchema({
      name: 'reminders',
      columns: [
        { name: 'user_id', type: 'string' },
        { name: 'reminder_type', type: 'string' },
        { name: 'schedule', type: 'string' },
        { name: 'status', type: 'string' },
        { name: 'created_at', type: 'number' },
        { name: 'updated_at', type: 'number' },
        { name: 'description', type: 'string', isOptional: true },
        { name: 'notification_id', type: 'number', isOptional: true },
      ],
    }),
  ],
});

export default schema;