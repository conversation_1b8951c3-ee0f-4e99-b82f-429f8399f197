import React, { useEffect } from 'react';
import { View, ActivityIndicator } from 'react-native';
import BaseLayout from '../layouts/BaseLayout';
import { ScaledSheet } from 'react-native-size-matters';
import Center from '../components/Center';
import BluetoothStore from '../stores/BluetoothStore';

const BluetoothLoadingScreen: React.FC = () => {

  useEffect(() => {
    BluetoothStore.init();
  }, []);

  return (
    <BaseLayout>
      <Center>
        <ActivityIndicator size="large" color="blue" />
      </Center>
    </BaseLayout>
  );
};

const styles = ScaledSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
});

export default BluetoothLoadingScreen;