import React, { useEffect } from 'react';
import { View, ActivityIndicator, FlatList, SafeAreaView } from 'react-native';
import BaseLayout from '../layouts/BaseLayout';
import { ScaledSheet } from 'react-native-size-matters';
import BluetoothStore from '../stores/BluetoothStore';
import { observer } from 'mobx-react-lite';
import P from '../components/typography/P';
import Header from '../components/Header';
import Button from '../components/Button';

const BluetoothLog: React.FC = observer(() => {

  useEffect(() => {

  }, []);

  const renderItem = ({ item, index }: { item: any, index: number }) => {

    return (
      <View key={index} style={{ padding: 10, borderBottomWidth: 1, borderBottomColor: '#ccc', backgroundColor: '#ccc', marginBottom: 10 }}>
        <P>{JSON.stringify(item)}</P>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <Header title="Bluetooth Logs" />
      <FlatList
        data={BluetoothStore.logs}
        keyExtractor={(item, index) => index.toString()}
        renderItem={renderItem}
        ListFooterComponentStyle={{
          padding: 20,
          alignItems: 'center',
          justifyContent: 'center',
        }}
      />
      <Button title='Clear logs' onPress={() => {
        BluetoothStore.clearLogs();
      }} />
    </SafeAreaView>
  );
});

const styles = ScaledSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    padding: '20@s',
    paddingBottom: '40@s',
  }
});

export default BluetoothLog;