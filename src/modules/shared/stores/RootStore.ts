import CareTakerStore from "../../menu/stores/CareTakerStore";
import UserStore from "../../menu/stores/UserStore";
import BluetoothStore from "./BluetoothStore";
import CartridgeStore from "./CartridgeStore";
import ReminderStore from "./ReminderStore";

export class RootStore {
  userStore: UserStore;
  cartridgeStore: CartridgeStore;
  reminderStore: ReminderStore;
  careTakerStore: CareTakerStore;

  constructor() {
    this.userStore = new UserStore(this);
    this.cartridgeStore = new CartridgeStore(this);
    this.reminderStore = new ReminderStore(this);
    this.careTakerStore = new CareTakerStore(this);
  }

  async initialize() {
    await BluetoothStore.init()
    await this.userStore.loadUser()
    await this.cartridgeStore.loadCartridges()
    await this.reminderStore.loadReminders()
  }
}

export default new RootStore();
