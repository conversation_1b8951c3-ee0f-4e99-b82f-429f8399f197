import {makeAutoObservable, runInAction} from 'mobx';
import Ble<PERSON><PERSON>ger, {Peripheral} from 'react-native-ble-manager';
import {
  CH_SERVICE_UUID,
  GET_LIVE_DATA_COMMAND_ID,
  RX_UUID,
  SERVICE_UUID,
  SMP_CHARACTERISTIC_UUID,
  SYNC_RTC_COMMAND_ID,
  TX_UUID,
} from '../globals';
import {Alert, Platform} from 'react-native';
import {constructBLEData} from '../utils';
import {decodeSMPResponse} from '../../../services/firmware/SMPDecoder';

class BluetoothStore {
  isConnected = false;
  deviceId: string | null = null;
  device: Peripheral | null = null;
  isScanning = false;
  logs: any[] = [];

  constructor() {
    makeAutoObservable(this);
  }

  async init() {
    if (Platform.OS === 'android') {
      const state = await BleManager.checkState();
      if (state !== 'on') {
        console.warn('Bluetooth is OFF, skipping BleManager.start()');
        Alert.alert(
          'Bluetooth Required',
          'Please turn on Bluetooth to use this feature.',
        );
        return;
      }
    }
    await BleManager.start({showAlert: false});

    BleManager.onDidUpdateState(this.handleBluetoothState);
    BleManager.onDidUpdateValueForCharacteristic(this.handleNotification);
    BleManager.onDisconnectPeripheral(this.handleDisconnection);

    this.checkBluetoothAndLoadDevice();
  }

  checkBluetoothAndLoadDevice = async () => {
    const state = await BleManager.checkState();
    if (state === 'on') {
      console.log('Bluetooth is enabled');
      this.loadDevice();
    } else {
      console.warn('Bluetooth is OFF');
    }
  };

  loadDevice = async () => {
    const connectedDevices = await BleManager.getConnectedPeripherals([
      SERVICE_UUID,
    ]);
    if (connectedDevices.length > 0) {
      const device = connectedDevices[0];
      console.log('Connected device:', device);
      runInAction(() => {
        this.deviceId = device.id;
        this.isConnected = true;
        this.device = device;
      });
    } else {
      console.log('No connected devices found');
      this.scanAndConnect();
    }
  };

  scanAndConnect = async () => {
    if (this.isScanning) return;

    console.log('Scanning for devices...');
    runInAction(() => {
      this.isScanning = true;
    });

    BleManager.scan([], 5, true).then(() => {
      console.log('Scan started...');
    });

    const discoverListener = BleManager.onDiscoverPeripheral(
      async (peripheral: Peripheral) => {
        if (peripheral.advertising?.serviceUUIDs?.includes(SERVICE_UUID)) {
          console.log('Discovered device:', peripheral);
          BleManager.stopScan();
          discoverListener.remove();
          runInAction(() => {
            this.isScanning = false;
          });

          await this.connect(peripheral);
        }
      },
    );

    setTimeout(() => {
      discoverListener.remove();
      runInAction(() => {
        this.isScanning = false;
      });
      console.log('Scan timeout reached');
    }, 5000);
  };

  connect = async (peripheral: Peripheral) => {
    try {
      const id = peripheral.id;
      await BleManager.connect(id);
      console.log('Connected to:', peripheral);

      runInAction(() => {
        this.deviceId = id;
        this.isConnected = true;
        this.device = peripheral;
      });

      const services = await BleManager.retrieveServices(id);
      console.log('Retrieved services:', services);
      services.characteristics?.forEach(async characteristic => {
        console.log('Characteristic:', characteristic);
        await this.subscribeToNotifications(
          id,
          characteristic.service,
          characteristic.characteristic,
        );
      });
      await this.syncRTC();
    } catch (error) {
      console.error('Connection error:', error);
      Alert.alert('Connection Failed', 'Unable to connect to the device.');
    }
  };

  disconnect = async () => {
    if (!this.deviceId) return;
    await BleManager.disconnect(this.deviceId);
    runInAction(() => {
      this.isConnected = false;
      this.deviceId = null;
      this.device = null;
    });
    console.log('Device disconnected manually');
  };

  subscribeToNotifications = async (
    id: string,
    serviceUuid: string,
    characteristicUuid: string,
  ) => {
    try {
      await BleManager.startNotification(id, serviceUuid, characteristicUuid);
      console.log('Subscribed to notifications');
    } catch (error) {
      console.error('Subscription error:', error);
    }
  };

  readData = async (): Promise<string | null> => {
    if (!this.deviceId) return null;
    try {
      const data = await BleManager.read(this.deviceId, SERVICE_UUID, TX_UUID);
      return data.join(',');
    } catch (error) {
      console.error('Read error:', error);
      return null;
    }
  };

  writeData = async (
    command_id: number,
    payload: string,
    read: boolean = false,
  ) => {
    if (!this.deviceId) return;
    try {
      const data = constructBLEData({
        commandID: command_id,
        payload,
        isWrite: !read,
      });
      console.log('Writing data:', data);
      const result = await BleManager.write(
        this.deviceId,
        CH_SERVICE_UUID,
        RX_UUID,
        data,
      );
      console.log('Write result:', result);
      console.log('Data written successfully');
    } catch (error) {
      console.error('Write error:', error);
    }
  };

  handleDisconnection = () => {
    console.warn('Device disconnected! Attempting to reconnect...');
    runInAction(() => {
      this.isConnected = false;
      this.deviceId = null;
      this.device = null;
    });
  };

  handleNotification = (data: {
    value: number[];
    peripheral: string;
    characteristic: string;
  }) => {
    if (data.characteristic == SMP_CHARACTERISTIC_UUID) {
      console.log('SMP Notification received', decodeSMPResponse(data.value));
      return;
    }
    const decodedData = String.fromCharCode(...data.value);
    console.log('Notification received:', decodedData);
    runInAction(() => {
      this.logs = [{decodedData, data}, ...this.logs];
    });
  };

  handleBluetoothState = (state: {state: string}) => {
    console.log('Bluetooth state changed:', state);
    if (state.state === 'on') {
      console.log('Bluetooth is ON');
      this.loadDevice();
    } else if (state.state === 'off') {
      console.warn('Bluetooth is OFF');
      Alert.alert(
        'Bluetooth Required',
        'Please turn on Bluetooth to use this feature.',
      );
      runInAction(() => {
        this.isConnected = false;
        this.deviceId = null;
        this.device = null;
      });
    }
  };

  clearLogs = () => {
    runInAction(() => {
      this.logs = [];
    });
  };

  syncRTC = async () => {
    try {
      if (!this.deviceId) return;
      console.log('Synchronizing RTC...', Math.floor(Date.now() / 1000));
      await this.writeData(
        SYNC_RTC_COMMAND_ID,
        Math.floor(Date.now() / 1000).toString(),
      );
    } catch (error) {
      console.error('RTC sync error:', error);
    }
  };

  getLiveData = async () => {
    try {
      if (!this.deviceId) return;
      console.log('Fetching live data...');
      const data = await this.writeData(GET_LIVE_DATA_COMMAND_ID, '', true);
    } catch (error) {
      console.error('Error fetching live data:', error);
    }
  };
}

export default new BluetoothStore();
