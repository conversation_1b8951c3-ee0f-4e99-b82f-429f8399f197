import { makeAutoObservable, runInAction } from "mobx";
import Cartridge from "../../../database/models/Cartridge";
import { groupBy } from "lodash";
import { RootStore } from "./RootStore";

export default class CartridgeStore {
  rootStore: RootStore;
  cartridges: Cartridge[] = [];
  activeCartridges: Cartridge[] = [];
  groupedCartridges: { [name: string]: Cartridge[] } = {};

  constructor(rootStore: RootStore) {
    makeAutoObservable(this);
    this.rootStore = rootStore;
  }

  async loadCartridges() {
    try {
      const cartridges = await this.rootStore.userStore.user?.cartridges.fetch();
      const activeCartridges = await this.rootStore.userStore.user!.fetchOrInstallCartridges()
      if (cartridges) {
        runInAction(() => {
          this.cartridges = cartridges;
          this.activeCartridges = activeCartridges;
          this.groupedCartridges = groupBy(cartridges, cartridge => cartridge.name);
        })
        console.log("Cartridges loaded:", this.cartridges.length);
        console.log("Active cartridges:", this.activeCartridges[0]);
        console.log("Grouped cartridges:", this.groupedCartridges);
      }
    } catch (error) {
      console.error("Failed to load cartridges:", error);
    }
  }

  async createCartridges(cartridgeType: string, quantity: number) {
    if (this.rootStore.userStore.user) {
      try {
        await this.rootStore.userStore.user.createCartridges(cartridgeType, quantity);
        console.log('Cartridges created:', cartridgeType, quantity);
        await this.loadCartridges();
      } catch (error) {
        console.error('Error creating cartridges:', error);
      }
    } else {
      console.error('User not found');
    }
  }
}
