import { Platform } from 'react-native';
import PushNotification, { Importance } from 'react-native-push-notification';
import { ANDROID_CHANNELS } from '../globals';
import { RootStore } from './RootStore';
import Reminder from '../../../database/models/Reminder';
import { uniqueId } from 'lodash';
import { makeAutoObservable } from 'mobx';

export default class ReminderStore {
  rootStore: RootStore;
  reminders: Reminder[] = [];
  constructor(rootStore: RootStore) {
    this.rootStore = rootStore;
    makeAutoObservable(this)
    this.setupAndroidNotificationChannel();
  }

  loadReminders = async () => {
    try {
      const reminders = await this.rootStore.userStore.user!.reminders.fetch();
      this.reminders = reminders;
      console.log('Loaded reminders:', this.reminders);
    }
    catch (error) {
      console.error('Error loading reminders:', error);
    }
  }

  scheduleReminder = async (title: string, message: string, date: Date, repeatType: 'day' | 'week' | undefined = undefined, channelId: string, type: string) => {
    try {
      let schedule = '';
      const hours = date.getHours();
      const minutes = date.getMinutes();
      const period = hours >= 12 ? 'PM' : 'AM';
      const formattedHours = hours % 12 || 12; // Convert to 12-hour format
      if (repeatType) {
        schedule = `every ${repeatType} at ${formattedHours}:${minutes.toString().padStart(2, '0')} ${period}`;
      } else {
        schedule = `at ${formattedHours}:${minutes.toString().padStart(2, '0')} ${period}`;
      }
      const reminder = await this.rootStore.userStore.user!.createReminder(
        type,
        schedule,
        title,
      )
      console.log('Reminder created:', reminder);
      this.reminders.push(reminder);
      PushNotification.localNotificationSchedule({
        id: reminder.notificationId,
        channelId, title, message, date, repeatType,
        allowWhileIdle: true,
      });
    } catch (error) {
      console.error('Error scheduling reminder:', error);
    }
  }

  cancelAllReminders() {
    PushNotification.cancelAllLocalNotifications();
  }

  setupAndroidNotificationChannel() {
    if (Platform.OS === 'android') {
      Object.keys(ANDROID_CHANNELS).forEach((channel) => {
        const typedChannel = channel as keyof typeof ANDROID_CHANNELS;
        PushNotification.createChannel(
          {
            channelId: ANDROID_CHANNELS[typedChannel].id,
            channelName: ANDROID_CHANNELS[typedChannel].name,
            channelDescription: ANDROID_CHANNELS[typedChannel].description,
            playSound: true,
            importance: Importance.HIGH,
            vibrate: true,
          },
          (created) => console.log(`createChannel for ${ANDROID_CHANNELS[typedChannel].name} returned '${created}'`),
        );
      });
    }
  }
}

