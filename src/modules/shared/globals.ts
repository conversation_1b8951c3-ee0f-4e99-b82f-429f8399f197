export const SERVICE_UUID = '17b695b5-f9a3-4037-802f-da33da618f0d';
export const CH_SERVICE_UUID = '17b695b1-f9a3-4037-802f-da33da618f0d';
export const TX_UUID = '17b695b2-f9a3-4037-802f-da33da618f0d';
export const RX_UUID = '17b695b3-f9a3-4037-802f-da33da618f0d';
export const SMP_SERVICE_UUID = '8d53dc1d-1db7-4cd3-868b-8a527460aa84';
export const SMP_CHARACTERISTIC_UUID = 'da2e7828-fbce-4e01-ae9e-261174997c48';
export const BOTTLE_CAPACITY_ML = 2000;
export const ANDROID_CHANNELS = {
  reminder: {
    id: 'snb-reminder-channel',
    name: 'Hydration Reminder',
    description: 'A channel to manage hydration reminders',
  },
  nutrition: {
    id: 'snb-nutrition-channel',
    name: 'Nutrition Reminder',
    description: 'A channel to manage nutrition notifications',
  },
};
export const REMINDER_TYPES = Object.freeze({
  hydration: 'hydration',
  nutrition: 'nutrition',
});
export const FREQUENCY_COMMAND_ID = 0x01;
export const DISPENSE_COMMAND_ID = 0x02;
export const SYNC_RTC_COMMAND_ID = 0x03;
export const GET_LIVE_DATA_COMMAND_ID = 0x04;
