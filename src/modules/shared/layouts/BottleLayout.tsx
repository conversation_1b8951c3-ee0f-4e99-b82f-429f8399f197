import React, { useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import Center from '../components/Center';
import Link from '../components/Link';
import H3 from '../components/typography/H3';
import { useNavigation } from '@react-navigation/native';
import { useStores } from '../../../RootStoreContext';

interface BottleLayoutProps {
  nutritionCompartment: React.ReactNode
  hydrationCompartment: React.ReactNode
  uvCompartment: React.ReactNode
}

const BottleLayout: React.FC<BottleLayoutProps> = ({ nutritionCompartment, hydrationCompartment, uvCompartment }) => {

  const navigation = useNavigation();
  const { userStore } = useStores();

  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      userStore.loadUser();
    });

    return unsubscribe;
  }, [navigation]);

  return (
    <View style={styles.container}>
      <View style={[styles.compartment, styles.nostril]}>
        <Center>
          <H3>{userStore.user?.first_name}</H3>
          <Link text="Edit Name" onPress={() => { navigation.navigate('EditProfile') }} />
          <Link text="BLE Logs" onPress={() => { navigation.navigate('BluetoothLog') }} />
        </Center>
      </View>

      <View style={[styles.compartment, styles.nutrition]}>
        {nutritionCompartment}
      </View>

      <View style={[styles.compartment, styles.hydration]}>
        {hydrationCompartment}
      </View>

      <View style={[styles.compartment, styles.uv]}>
        {uvCompartment}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'column',
  },
  compartment: {
    borderWidth: 2,
    borderColor: 'black',
  },
  nostril: {
    flex: 0.2,
    backgroundColor: 'white',
    borderBottomWidth: 0,
  },
  nutrition: {
    flex: 0.4,
    backgroundColor: '#e9b0af',
    padding: 10,
    borderBottomWidth: 0,
  },
  hydration: {
    flex: 0.4,
    backgroundColor: '#8be2ec',
    padding: 10,
    borderBottomWidth: 0,
  },
  uv: {
    flex: 0.1,
    backgroundColor: '#b4ee9d',
    padding: 10,
    paddingBottom: 20,
    borderBottomRightRadius: 50,
    borderBottomLeftRadius: 50,
  },
  editNameLink: {
    fontSize: 14,
    color: 'black',
    textDecorationLine: 'underline',
  },
  bottomLeftText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default BottleLayout;