import React from 'react';
import {
  TouchableOpacity,
  View,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
} from 'react-native';

interface CheckBoxProps {
  label: string;
  checked: boolean;
  onChange: (checked: boolean) => void;
  disabled?: boolean;
  style?: ViewStyle;
  labelStyle?: TextStyle;
  boxStyle?: ViewStyle;
}

const CheckBox: React.FC<CheckBoxProps> = ({
  label,
  checked,
  onChange,
  disabled = false,
  style,
  labelStyle,
  boxStyle,
}) => {
  return (
    <TouchableOpacity
      onPress={() => !disabled && onChange(!checked)}
      activeOpacity={0.8}
      style={[styles.container, style]}
      disabled={disabled}
    >
      <View style={[styles.checkbox, checked && styles.checkedBox, boxStyle]}>
        {checked && <View style={styles.tickMark} />}
      </View>
      <Text style={[styles.label, disabled && styles.disabledLabel, labelStyle]}>
        {label}
      </Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: '#555',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  checkedBox: {
    backgroundColor: '#007bff',
    borderColor: '#007bff',
  },
  tickMark: {
    width: 10,
    height: 10,
    backgroundColor: '#fff',
    borderRadius: 2,
  },
  label: {
    fontSize: 16,
    color: '#333',
  },
  disabledLabel: {
    color: '#999',
  },
});

export default CheckBox;
