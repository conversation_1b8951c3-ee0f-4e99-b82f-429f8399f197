import React from "react";
import { View, Text, StyleSheet } from "react-native";
import { scale, ScaledSheet } from "react-native-size-matters";

type ProgressBarProps = {
  color: string;
  percentage: number;
  height?: number;
  label?: string;
};

const ProgressBar: React.FC<ProgressBarProps> = ({ color, percentage, height, label}) => {
  return (
    <>
    <View style={styles.container}>
      <View style={[styles.barContainer, { borderColor: color }]}>
        <View style={[styles.bar, { width: `${percentage}%`, backgroundColor: color, height: height || 10 }]} >
          <Text style={{color:'#ddd', fontWeight: 500, margin: scale(5)}} >{label || ''}</Text>
        </View>
      </View>
    </View>
    <Text style={styles.percentageText}>{percentage}%</Text>
    </>
  );
};

const styles = ScaledSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
  },
  barContainer: {
    flex: 1,
    backgroundColor: "#fff",
    borderWidth: 1,
  },
  bar: {
    height: "100%",
  },
  percentageText: {
    margin: 3,
    fontSize: '14@s',
    fontWeight: "bold",
    textAlign: 'right'
  },
});

export default ProgressBar;
