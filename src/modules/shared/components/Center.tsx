import React from "react";
import { View, StyleSheet, ViewStyle } from "react-native";

interface CenterProps {
  children: React.ReactNode;
  vertical?: boolean;
  horizontal?: boolean;
}

const Center: React.FC<CenterProps> = ({ children, vertical = true, horizontal = true }) => {
  const containerStyle: ViewStyle = {
    flex: 1,
    justifyContent: vertical ? "center" : "flex-start",
    alignItems: horizontal ? "center" : "flex-start",
  };

  return <View style={containerStyle}>{children}</View>;
};

export default Center;
