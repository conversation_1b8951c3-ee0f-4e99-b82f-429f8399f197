import React from "react";
import { View, StyleSheet } from "react-native";
import Modal from "react-native-modal";
import Button from "./Button";
import Gutter from "./Gutter";
import P from "./typography/P";
import { ScaledSheet } from "react-native-size-matters";

interface AlertPopoverProps {
  visible: boolean;
  onClose: () => void;
  backgroundColor?: string;
  title?: string;
  content?: string | JSX.Element;
  image?: JSX.Element;
  buttons?: { text: string; onPress: () => void; color?: string, disabled: boolean }[];
  customContent?: JSX.Element;
}

const AlertPopover: React.FC<AlertPopoverProps> = ({
  visible,
  onClose,
  backgroundColor = "#fff",
  title,
  content,
  image,
  buttons = [],
  customContent
}) => {
  return (
    <Modal isVisible={visible} onBackdropPress={onClose} animationIn="zoomIn" animationOut="zoomOut">
      <View style={[styles.container, { backgroundColor }]}>
        {image}
        <Gutter size={20} />
        {title && <P style={styles.title}>{title}</P>}
        {content && <P style={styles.content}>{content}</P>}
        {customContent}
        <View style={styles.buttonContainer}>
          {buttons.map((button, index) => (
            <Button key={`alert_button_${index}`} disabled={button.disabled} title={button.text} variant="outline" onPress={button.onPress} style={{ borderColor: '#fff', paddingVertical: 5 }} textStyle={{ color: '#fff'}} />
          ))}
        </View>
      </View>
    </Modal>
  );
};

const styles = ScaledSheet.create({
  container: {
    alignSelf: "center",
    padding: 20,
    borderRadius: 10,
    alignItems: "center",
  },
  image: {
    width: '80@s',
    height: '80@s',
    marginBottom: 10,
  },
  title: {
    fontSize: '18@s',
    fontWeight: "bold",
    marginBottom: 8,
    textAlign: "center",
    color: "#fff"
  },
  content: {
    fontSize: '14@s',
    textAlign: "center",
    marginBottom: 15,
    color: "#fff",
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "center",
    marginTop: 10,
  },
  button: {
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 5,
    marginHorizontal: 5,
  },
  buttonText: {
    color: "#fff",
    fontWeight: "bold",
    textAlign: "center",
  },
});

export default AlertPopover;
