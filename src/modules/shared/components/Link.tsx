import React from 'react';
import { StyleSheet, TextProps, GestureResponderEvent } from 'react-native';
import P from './typography/P';

interface LinkProps extends TextProps {
  color?: string;
  text: string;
  onPress?: (event: GestureResponderEvent) => void;
}

const Link: React.FC<LinkProps> = ({ color = 'black', text, onPress, style, ...props }) => {
  return (
    <P style={[styles.link, { color }, style]} onPress={onPress} {...props}>
      {text}
    </P>
  );
};

const styles = StyleSheet.create({
  link: {
    textDecorationLine: 'underline',
    color: 'black',
  },
});

export default Link;