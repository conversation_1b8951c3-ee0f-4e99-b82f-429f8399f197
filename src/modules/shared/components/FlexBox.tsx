import React from "react";
import { View, StyleSheet, ViewStyle } from "react-native";

interface FlexBoxProps {
  children: React.ReactNode;
  direction?: "row" | "column";
  justify?: "flex-start" | "center" | "flex-end" | "space-between" | "space-around" | "space-evenly";
  align?: "flex-start" | "center" | "flex-end" | "stretch" | "baseline";
  style?: ViewStyle;
}

const FlexBox: React.FC<FlexBoxProps> = ({
  children,
  direction = "column",
  justify = "flex-start",
  align = "flex-start",
  style = {},
}) => {
  return (
    <View style={[{ flexDirection: direction, justifyContent: justify, alignItems: align }, style]}>
      {children}
    </View>
  );
};

export default FlexBox;
