import React from "react";
import { View } from "react-native";
import { scale } from "react-native-size-matters";

interface GutterProps {
  size?: number;
  direction?: "horizontal" | "vertical";
}

const Gutter: React.FC<GutterProps> = ({ size = 8, direction = "vertical" }) => {
  return (
    <View
      style={{
        width: direction === "horizontal" ? scale(size) : 0,
        height: direction === "vertical" ? scale(size) : 0,
      }}
    />
  );
};

export default Gutter;
