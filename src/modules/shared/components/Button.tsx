import React from 'react';
import { TouchableOpacity, ViewStyle, TextStyle, View } from 'react-native';
import Icon from '@react-native-vector-icons/ionicons'; // You can use any icon set
import P from './typography/P';
import {scale, ScaledSheet} from 'react-native-size-matters'
 
interface ButtonProps {
  title: string;
  onPress?: () => void;
  color?: string; 
  variant?: 'filled' | 'outline';
  disabled?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
  stickyPosition?: 'top' | 'bottom';
  icon?: string; 
  iconPosition?: 'left' | 'right';
  small?: boolean
}

const Button: React.FC<ButtonProps> = ({
  title,
  onPress,
  color = '#000',
  variant = 'filled',
  disabled = false,
  style,
  textStyle,
  stickyPosition,
  icon,
  iconPosition = 'left', // Default icon on the left
  small
}) => {
  const isOutline = variant === 'outline';

  return (
    <TouchableOpacity
      onPress={!disabled ? onPress : undefined}
      activeOpacity={disabled ? 1 : 0.7}
      style={[
        styles.button,
        { backgroundColor: isOutline ? 'transparent' : color, borderColor: color },
        isOutline && styles.outline,
        disabled && styles.disabled,
        stickyPosition === 'top' && styles.stickyTop,
        stickyPosition === 'bottom' && styles.stickyBottom,
        small && { paddingVertical: 2, paddingHorizontal: 6 },
        style,
      ]}
      disabled={disabled}
    >
      <View style={styles.content}>
        {icon && iconPosition === 'left' && (
          <Icon name={icon} size={scale(20)} color={disabled ? '#aaa' : isOutline ? color : '#FFF'} style={styles.icon} />
        )}
        <P style={[styles.text, { color: disabled ? '#aaa' : isOutline ? color : '#FFF' }, textStyle, small && styles.small,]}>
          {title}
        </P>
        {icon && iconPosition === 'right' && (
          <Icon name={icon} size={scale(20)} color={disabled ? '#aaa' : isOutline ? color : '#FFF'} style={styles.icon} />
        )}
      </View>
    </TouchableOpacity>
  );
};

const styles = ScaledSheet.create({
  button: {
    width: 'auto',
    paddingVertical: '5@vs',
    paddingHorizontal: '24@s',
    borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  outline: {
    backgroundColor: 'transparent',
  },
  text: {
    fontSize: '16@s',
  },
  disabled: {
    opacity: 0.5,
  },
  stickyTop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 10,
  },
  stickyBottom: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    zIndex: 10,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  icon: {
    marginRight: '8@s',
    marginLeft: '8@s',
  },
  small:{
    fontSize: '9@s',
    paddingVertical: '2@vs',
    paddingHorizontal: '2@s',
    lineHeight: '12@vs',
  }
});

export default Button;
