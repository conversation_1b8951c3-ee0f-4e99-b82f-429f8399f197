import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import Icon from '@react-native-vector-icons/feather';
import P from './typography/P';

interface DropDownProps {
  options: string[];
  selectedOption: string;
  onSelect: (option: string) => void;
  label: string;
  darkContent?: boolean;
}

const DropDown: React.FC<DropDownProps> = ({ options, selectedOption, onSelect, label, darkContent = true }) => {
  const [isOpen, setIsOpen] = React.useState(false);

  const handleSelect = (option: string) => {
    onSelect(option);
    setIsOpen(false);
  };

  const textColor = darkContent ? '#000' : '#fff';
  const borderColor = darkContent ? '#000' : '#fff';

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={[styles.selectedOption, { borderColor }]}
        onPress={() => setIsOpen(!isOpen)}
      >
        <Text style={{ color: textColor }}>{selectedOption || label}</Text>
        <Icon name="chevron-down" size={24} color={textColor} style={{ marginLeft: 10 }} />
      </TouchableOpacity>
      {isOpen && (
        <View style={[styles.optionsContainer, { borderColor }]}>
          {options.map((option, index) => (
            <TouchableOpacity
              key={index}
              style={styles.option}
              onPress={() => handleSelect(option)}
            >
              <P style={{ color: textColor }}>{option}</P>
            </TouchableOpacity>
          ))}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: 'auto',
  },
  selectedOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 5,
    paddingHorizontal: 10,
    borderWidth: 1,
  },
  optionsContainer: {
    borderWidth: 1,
  },
  option: {
    padding: 5,
  },
});

export default DropDown;