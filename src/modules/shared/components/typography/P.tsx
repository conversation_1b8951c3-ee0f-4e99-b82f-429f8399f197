import React from 'react';
import { Text, StyleSheet, TextProps } from 'react-native';
import { ScaledSheet } from 'react-native-size-matters';

interface PProps extends TextProps {
  children: React.ReactNode;
}

const P: React.FC<PProps> = ({ children, style, ...props }) => {
  return (
    <Text style={[styles.paragraph, style]} {...props}>
      {children}
    </Text>
  );
};

const styles = ScaledSheet.create({
  paragraph: {
    fontSize: '12@s',
    lineHeight: '24@s',
    color: '#000',
    fontFamily: 'OpenSans-Regular'
  },
});

export default P;