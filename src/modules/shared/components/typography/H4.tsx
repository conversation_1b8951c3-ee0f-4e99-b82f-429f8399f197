import React from 'react';
import { Text, TextProps, StyleSheet } from 'react-native';
import { ScaledSheet } from 'react-native-size-matters';

interface H4Props extends TextProps {
  children: React.ReactNode;
}

const H4: React.FC<H4Props> = ({ children, style, ...props }) => {
  return (
    <Text style={[styles.h4, style]} {...props}>
      {children}
    </Text>
  );
};

const styles = ScaledSheet.create({
  h4: {
    fontSize: '16@s',
    fontFamily: 'OpenSans-Bold'
  },
});

export default H4;