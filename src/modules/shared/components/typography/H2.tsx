import React from 'react';
import { Text, TextProps, StyleSheet } from 'react-native';
import { ScaledSheet } from 'react-native-size-matters';

interface H2Props extends TextProps {
  children: React.ReactNode;
}

const H2: React.FC<H2Props> = ({ children, style, ...props }) => {
  return (
    <Text style={[styles.h2, style]} {...props}>
      {children}
    </Text>
  );
};

const styles = ScaledSheet.create({
  h2: {
    fontSize: '24@s',
    color: '#000',
    fontFamily: 'OpenSans-Bold'
  },
});

export default H2;