import React from 'react';
import { Text, StyleSheet } from 'react-native';
import { ScaledSheet } from 'react-native-size-matters';

interface H1Props {
  children: React.ReactNode;
  style?: object;
}

const H1: React.FC<H1Props> = ({ children, style }) => {
  return <Text style={[styles.h1, style]}>{children}</Text>;
};

const styles = ScaledSheet.create({
  h1: {
    fontSize: '32@s',
    color: '#000',
    fontFamily: 'OpenSans-Bold'
  },
});

export default H1;