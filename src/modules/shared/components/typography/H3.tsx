import React from 'react';
import { Text, TextProps, StyleSheet } from 'react-native';
import { ScaledSheet } from 'react-native-size-matters';

interface H3Props extends TextProps {
  children: React.ReactNode;
}

const H3: React.FC<H3Props> = ({ children, style, ...props }) => {
  return (
    <Text style={StyleSheet.flatten([styles.h3, style])} {...props}>
      {children}
    </Text>
  );
};

const styles = ScaledSheet.create({
  h3: {
    fontSize: '20@s',
    fontFamily: 'OpenSans-Bold'
  },
});

export default H3;