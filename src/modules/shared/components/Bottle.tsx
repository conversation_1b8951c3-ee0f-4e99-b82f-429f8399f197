import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useStores } from '../../../RootStoreContext';


const Bottle = () => {

  const { userStore } = useStores();

  return (
    <View style={styles.container}>
      <View style={[styles.compartment, styles.nostril]}>
        <Text style={styles.userName}>{userStore?.user?.first_name}</Text>
        <TouchableOpacity onPress={()=>{}}>
          <Text style={styles.editNameLink}>Edit name</Text>
        </TouchableOpacity>
      </View>

      <View style={[styles.compartment, styles.nutrition]}>
        <Text style={styles.bottomLeftText}>Nutrition</Text>
      </View>

      <View style={[styles.compartment, styles.hydration]}>
        <Text style={styles.bottomLeftText}>Hydration</Text>
      </View>

      <View style={[styles.compartment, styles.uv]}>
        <Text style={styles.bottomLeftText}>UV</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'column',
  },
  compartment: {
    borderWidth: 2,
    borderColor: 'black',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
  nostril: {
    flex: 0.2,
    backgroundColor: 'white',
    borderBottomWidth: 0,
  },
  nutrition: {
    flex: 0.4,
    backgroundColor: 'lightpink',
    justifyContent: 'flex-end',
    alignItems: 'flex-start',
    padding: 10,
    borderBottomWidth: 0,
  },
  hydration: {
    flex: 0.4,
    backgroundColor: 'lightblue',
    justifyContent: 'flex-end',
    alignItems: 'flex-start',
    padding: 10,
    borderBottomWidth: 0,
  },
  uv: {
    flex: 0.1,
    backgroundColor: 'lightgreen',
    justifyContent: 'flex-end',
    alignItems: 'flex-start',
    padding: 10,
    paddingBottom: 20,
    borderBottomRightRadius: 50,
    borderBottomLeftRadius: 50,
  },
  userName: {
    fontSize: 16,
    fontWeight: '500',
    textTransform: 'capitalize',
  },
  editNameLink: {
    fontSize: 14,
    color: 'black',
    textDecorationLine: 'underline',
  },
  bottomLeftText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default Bottle;