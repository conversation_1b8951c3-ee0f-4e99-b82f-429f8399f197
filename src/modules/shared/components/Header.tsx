import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Icon from '@react-native-vector-icons/ionicons';
import { useNavigation } from '@react-navigation/native';
import H3 from './typography/H3';
import {scale} from 'react-native-size-matters'

interface HeaderProps {
  title: string;
}

const Header: React.FC<HeaderProps> = ({ title }) => {

  const navigation = useNavigation();

  return (
    <View style={styles.container}>
      <Icon name='return-up-back-outline' size={scale(30)} onPress={()=>{navigation.goBack()}} />
      <H3>{title}</H3>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#fff',
  },
});

export default Header;