import {<PERSON><PERSON>er} from 'buffer';
import BleManager from 'react-native-ble-manager';
import {buildSMPMessage} from '../../services/firmware/SMPEncoder';

export interface BLEDataParams {
  commandID: number;
  payload: string; // Payload is always a string
  isWrite?: boolean;
}

/**
 * Constructs a BLE frame using the specified protocol with a string payload.
 *
 * Frame Format:
 *   SOF_L (0xDE) - Start of Frame (Low byte)
 *   SOF_H (0xAD) - Start of Frame (High byte)
 *   CMD    (1 byte) - Command ID
 *   LEN    (1 byte) - Payload length
 *   PAYLOAD (up to 255 bytes) - String payload (ASCII encoded)
 *   RW     (1 byte) - Read/Write flag (0x00 = Read, 0x01 = Write)
 *   EOF_L (0xFE) - End of Frame (Low byte)
 *   EOF_H (0xED) - End of Frame (High byte)
 */

function toLittleEndianBytes(num: number): number[] {
  const bytes = [];

  for (let i = 0; i < 4; i++) {
    // Extract each byte using bitwise operations
    bytes.push((num >> (8 * i)) & 0xff);
  }

  return bytes;
}

export const constructBLEData = ({
  commandID,
  payload = '',
  isWrite = true,
}: BLEDataParams): number[] => {
  const SOF_L = 0xde; // Start of Frame (Low byte)
  const SOF_H = 0xad; // Start of Frame (High byte)
  const EOF_L = 0xfe; // End of Frame (Low byte)
  const EOF_H = 0xed; // End of Frame (High byte)
  const RW = isWrite ? 0x01 : 0x00; // Read/Write flag

  // Convert the string payload to hex
  console.log('parsed payload:', parseInt(payload));
  const payloadBytes = toLittleEndianBytes(parseInt(payload));
  console.log('Payload bytes:', payloadBytes);
  console.log(
    payloadBytes.map(b => '0x' + b.toString(16).padStart(2, '0').toUpperCase()),
  );
  // Validate payload length
  if (payloadBytes.length > 255) {
    throw new Error('Payload too long. Maximum allowed is 255 bytes.');
  }

  const payloadLength = payloadBytes.length;
  const totalFrameLength = 2 + 1 + 1 + payloadLength + 1 + 2; // SOF + CMD + LEN + PAYLOAD + RW + EOF

  // Allocate a buffer for the frame
  const frameBuffer = Buffer.alloc(totalFrameLength);
  let offset = 0;

  // Write frame components to the buffer
  frameBuffer.writeUInt8(SOF_L, offset++); // Start of Frame (Low byte)
  frameBuffer.writeUInt8(SOF_H, offset++); // Start of Frame (High byte)
  frameBuffer.writeUInt8(commandID, offset++); // Command ID
  frameBuffer.writeUInt8(payloadLength, offset++); // Payload length

  // Write payload bytes
  for (const byte of payloadBytes) {
    frameBuffer.writeUInt8(byte, offset++);
  }

  frameBuffer.writeUInt8(RW, offset++); // Read/Write flag
  frameBuffer.writeUInt8(EOF_L, offset++); // End of Frame (Low byte)
  frameBuffer.writeUInt8(EOF_H, offset++); // End of Frame (High byte)

  // Convert the buffer to an array of numbers and return
  return Array.from(frameBuffer);
};


export async function testFirmwareUpload(
  peripheralId: string,
  serviceUUID: string,
  characteristicUUID: string
) {
  // Dummy firmware (32 bytes): 0x00 to 0x1F
  const firmwareData = Buffer.from(Array.from({ length: 32 }, (_, i) => i));
  const firmwareSize = firmwareData.length;

  // First 32 bytes chunk
  const chunk = firmwareData.slice(0, 32);

  // Precomputed SHA256 hash of the above buffer
  const sha256 = Buffer.from([
    0x8e, 0x95, 0x9b, 0x75, 0xda, 0xe3, 0x13, 0xda,
    0x8c, 0xf4, 0xf7, 0x28, 0x14, 0xfc, 0x14, 0x30,
    0xf8, 0xf7, 0x77, 0x9c, 0xb3, 0x1e, 0x5e, 0x3d,
    0x70, 0xd7, 0xe8, 0xb7, 0xe5, 0xe3, 0x81, 0x78,
  ]);

  const sequence = 0;
  const offset = 0;

  const message = buildSMPMessage({
    offset,
    chunk,
    firmwareSize,
    sha256,
    sequence,
  });

  console.log('Raw SMP Message Length:', message.length);
  console.log('Raw SMP Message (hex):', message.toString('hex'));
  console.log('Byte array:', Array.from(message));

  await BleManager.writeWithoutResponse(
    peripheralId,
    serviceUUID,
    characteristicUUID,
    Array.from(message),
  );

  console.log('✅ Test SMP firmware write sent — wait for notification...');
}



