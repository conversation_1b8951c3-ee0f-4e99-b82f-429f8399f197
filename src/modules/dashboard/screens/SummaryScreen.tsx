import React, {useEffect} from 'react';
import {View} from 'react-native';
import BaseLayout from '../../shared/layouts/BaseLayout';
import Header from '../../shared/components/Header';
import H1 from '../../shared/components/typography/H1';
import H3 from '../../shared/components/typography/H3';
import DropDown from '../../shared/components/DropDown';
import Gutter from '../../shared/components/Gutter';
import SegmentedControl from '@react-native-segmented-control/segmented-control';
import {BarChart} from 'react-native-gifted-charts';
import H2 from '../../shared/components/typography/H2';
import P from '../../shared/components/typography/P';
import {observer} from 'mobx-react-lite';
import {ScaledSheet} from 'react-native-size-matters';
import {useStores} from '../../../RootStoreContext';
import {Calendar} from 'react-native-calendars';

const SummaryScreen: React.FC = observer(() => {
  const [selectedIndex, setSelectedIndex] = React.useState(0);
  const [barData, setBarData] = React.useState<
    {value: number; label: string}[]
  >([
    {value: 0, label: 'M'},
    {value: 0, label: 'T'},
    {value: 0, label: 'W'},
    {value: 0, label: 'T'},
    {value: 0, label: 'F'},
    {value: 0, label: 'S'},
    {value: 0, label: 'S'},
  ]);

  const {
    cartridgeStore,
    userStore: {user},
  } = useStores();
  const [selectedOption, setSelectedOption] = React.useState(
    Object.keys(cartridgeStore.groupedCartridges)[0],
  );
  const [markedDates, setMarkedDates] = React.useState<{
    [key: string]: {
      customStyles: {
        container: {backgroundColor: string};
        text: {color: string; fontWeight: string};
      };
    };
  }>({});

  const updateBarData = (dates: string[]) => {
    const counts = [0, 0, 0, 0, 0, 0, 0];
    dates.forEach(dateStr => {
      const date = new Date(dateStr);
      const day = date.getDay();
      counts[day]++;
    });
    // Map to barData: [Mon, Tue, Wed, Thu, Fri, Sat, Sun]
    const labels = ['M', 'T', 'W', 'T', 'F', 'S', 'S'];
    // getDay() is 0=Sun, so shift accordingly
    const orderedCounts = [
      counts[1],
      counts[2],
      counts[3],
      counts[4],
      counts[5],
      counts[6],
      counts[0],
    ];
    setBarData(labels.map((label, i) => ({label, value: orderedCounts[i]})));
  };

  const loadMarkedDates = async () => {
    const dates = await user!.getDatesTracked(selectedOption);
    updateBarData(dates);
    const marks = dates.reduce((acc, date) => {
      acc[date] = {
        customStyles: {
          container: {
            backgroundColor: 'black',
          },
          text: {
            color: 'black',
            fontWeight: 'bold',
          },
        },
      };
      return acc;
    }, {} as Record<string, {customStyles: {container: {backgroundColor: string}; text: {color: string; fontWeight: string}}}>);
    console.log('Marked dates:', marks);

    setMarkedDates(marks);
  };

  useEffect(() => {
    loadMarkedDates();
  }, [selectedOption]);

  return (
    <BaseLayout>
      <Header title="Tablet Tracking" />
      <H1>Summaries</H1>
      <H3>Nutrition</H3>
      <Gutter size={30} />
      <View style={{width: '60%'}}>
        <DropDown
          options={Object.keys(cartridgeStore.groupedCartridges)}
          onSelect={option => setSelectedOption(option)}
          selectedOption={selectedOption}
          label="Select"
        />
      </View>
      <Gutter size={20} />
      <SegmentedControl
        values={['D', 'W', 'M']}
        selectedIndex={selectedIndex}
        onChange={event => {
          setSelectedIndex(event.nativeEvent.selectedSegmentIndex);
        }}
        style={{borderRadius: 0}}
        tintColor="#000"
        backgroundColor="#fff"
        fontStyle={{color: '#000'}}
        activeFontStyle={{color: '#fff'}}
        tabStyle={{borderRadius: 0, borderWidth: 1}}
        sliderStyle={{borderRadius: 0}}
      />
      <Gutter size={20} />
      <BarChart
        data={barData}
        dashWidth={0}
        formatYLabel={value => ``}
        isAnimated={true}
        endSpacing={20}
        barWidth={20}
        initialSpacing={30}
      />
      <Gutter size={20} />
      <H2>Highlights</H2>
      <Gutter size={10} />
      <P>In the last 7 days, you've consumed 1300 calories daily</P>
      <Gutter size={10} />
      <P>You hit your goals on 20 dats in the last 4 weeks</P>
      <Calendar
        headerStyle={{backgroundColor: '#fff'}}
        disableMonthChange={true}
        hideArrows={true}
        markingType={'custom'}
        markedDates={markedDates}
      />
      <Gutter size={40} />
    </BaseLayout>
  );
});

const styles = ScaledSheet.create({});

export default SummaryScreen;
