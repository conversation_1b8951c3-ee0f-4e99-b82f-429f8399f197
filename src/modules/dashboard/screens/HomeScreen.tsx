import React from 'react';
import {View, StyleSheet, Text} from 'react-native';
import {observer} from 'mobx-react-lite';
import {useNavigation} from '@react-navigation/native';
import Icon from '@react-native-vector-icons/feather';
import BluetoothStore from '../../shared/stores/BluetoothStore';
import PlaceHolderBottle from '../components/PlaceHolderBottle';
import Button from '../../shared/components/Button';
import ProgressBar from '../../shared/components/ProgressBar';
import BottleLayout from '../../shared/layouts/BottleLayout';
import HydrationCompartment from '../components/HydrationCompartment';
import UVCompartment from '../components/UVCompartment';
import NutritionCompartment from '../components/NutritionCompartment';
import {scale} from 'react-native-size-matters';
import {useStores} from '../../../RootStoreContext';
import {Q} from '@nozbe/watermelondb';

const HomeScreen = observer(() => {
  const navigation = useNavigation();
  const {userStore, cartridgeStore} = useStores();

  const renderHeader = () => {
    return (
      <View style={styles.header}>
        <Text style={styles.title}>Home</Text>
        <Icon
          name="grid"
          size={24}
          onPress={() => {
            navigation.navigate('Menu');
          }}
        />
      </View>
    );
  };

  const renderBottleWithActions = () => {
    return (
      <>
        <View
          style={{
            justifyContent: 'center',
            flex: 0.9,
            paddingVertical: 20,
            flexDirection: 'row',
            width: '100%',
            alignContent: 'center',
            alignItems: 'center',
          }}>
          <Icon name="chevron-left" size={50} />
          <View style={{flex: 1, width: '100%'}}>
            <BottleLayout
              nutritionCompartment={
                <NutritionCompartment
                  cartridge={cartridgeStore.activeCartridges[0]}
                />
              }
              hydrationCompartment={<HydrationCompartment />}
              uvCompartment={<UVCompartment />}
            />
          </View>
          <Icon name="chevron-right" size={50} />
        </View>
        <View style={{padding: 20, paddingHorizontal: 40}}>
          <ProgressBar
            color="black"
            percentage={BluetoothStore.device?.advertising.txPowerLevel || 0}
            height={scale(30)}
            label="Battery"
          />
        </View>
      </>
    );
  };

  return (
    <>
      <View style={styles.container}>
        {renderHeader()}
        {true ? renderBottleWithActions() : <PlaceHolderBottle />}
        <Button
          title={BluetoothStore.isConnected ? 'Disconnect' : 'Connect'}
          icon="bluetooth"
          stickyPosition="bottom"
          onPress={() => {
            BluetoothStore.isConnected
              ? BluetoothStore.disconnect()
              : BluetoothStore.scanAndConnect();
          }}
        />
      </View>
    </>
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
    padding: 10,
  },
  text: {
    fontSize: 18,
    marginBottom: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 10,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  carouselContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
  },
  arrowLeft: {
    position: 'absolute',
    left: 10,
    zIndex: 1,
    padding: 10,
  },
  arrowRight: {
    position: 'absolute',
    right: 10,
    zIndex: 1,
    padding: 10,
  },
});

export default HomeScreen;
