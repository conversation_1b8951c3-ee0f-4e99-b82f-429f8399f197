import React from 'react';
import { Text, StyleSheet, Image } from 'react-native';
import Header from '../../shared/components/Header';
import Center from '../../shared/components/Center';
import Gutter from '../../shared/components/Gutter';
import BaseLayout from '../../shared/layouts/BaseLayout';
import H1 from '../../shared/components/typography/H1';
import P from '../../shared/components/typography/P';

const UVScreen: React.FC = () => {
  return (
    <BaseLayout >
      <Header title="UV Purification" />
      <Center horizontal vertical={false}>
        <Gutter size={40} />
        <Image source={require('../../../../assets/images/uv.png')} resizeMode="contain" style={{ width: '100%', height: '100%' }}  />
        <H1>UV</H1>
        <Gutter size={20} />
        <P style={styles.text} >
          Ultraviolet radiation renders bacteria, viruses, parasites, and fungi unable to replicate by damaging the nucleic acids of their DNA.
        </P>
        <Gutter />
        <P style={styles.text} >Auto-triggers before Tablet dispensing</P>
        <Gutter />
        <P style={styles.text} >
          <P style={{fontWeight: 'bold'}} >Precautions:</P> Make sure there is enough water before starting UV. PLace the bottle on a steady surface, do not hold the bottle. And do not open the bottle for any reason while UV is functioning.
        </P>
      </Center>
    </BaseLayout>
  )
};

const styles = StyleSheet.create({
  text: {
    textAlign: 'center'
  },
});

export default UVScreen