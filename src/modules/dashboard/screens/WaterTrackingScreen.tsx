import React from 'react';
import { Image } from 'react-native';
import BaseLayout from '../../shared/layouts/BaseLayout';
import Header from '../../shared/components/Header';
import P from '../../shared/components/typography/P';
import H2 from '../../shared/components/typography/H2';
import Link from '../../shared/components/Link';
import Center from '../../shared/components/Center';
import Gutter from '../../shared/components/Gutter';
import H3 from '../../shared/components/typography/H3';
import FlexBox from '../../shared/components/FlexBox';
import H1 from '../../shared/components/typography/H1';
import Button from '../../shared/components/Button';
import { useNavigation } from '@react-navigation/native';
import { observer } from 'mobx-react-lite';
import { useStores } from '../../../RootStoreContext';
import { round } from 'lodash';
import { BOTTLE_CAPACITY_ML } from '../../shared/globals';

const WaterTrackingScreen: React.FC = observer(() => {

  const navigation = useNavigation();
  const { userStore } = useStores()
  const bottlesToGo = Math.ceil(3000 / BOTTLE_CAPACITY_ML)
  const bottlesCompleted = Math.floor(userStore.totalWaterIntake / BOTTLE_CAPACITY_ML);

  return (
    <BaseLayout>
      <Header title="Water Tracking" />
      <Center>
        <Gutter size={40} />
        <Image source={require('../../../../assets/images/water_goal.png')} style={{ width: 200, height: 200 }} />
        <Gutter size={20} />
        <P>Daily Goal</P>
        <H2>3L</H2>
        <Link onPress={() => { }} text='Edit Goal' />
        <Gutter size={40} />
        <H3 style={{ fontWeight: 'bold' }} >Highlights</H3>
        <Gutter size={10} />
        <FlexBox direction="row" justify="space-around" align="center" style={{ flex: 1, width: '100%', backgroundColor: 'black', padding: 10 }} >
          <FlexBox justify='center' align='center' >
            <H1 style={{ color: 'white' }} >{userStore.dayStreak}</H1>
            <P style={{ color: 'white' }} >Day Streak</P>
          </FlexBox>
          <FlexBox justify='center' align='center' >
            <H1 style={{ color: 'white' }} >{round((userStore.totalWaterIntake / 3000) * 100)}%</H1>
            <P style={{ color: 'white' }} >of goal completed</P>
          </FlexBox>
          <FlexBox justify='center' align='center' >
            <H1 style={{ color: 'white' }} >{bottlesCompleted}</H1>
            <P style={{ color: 'white' }} >{bottlesToGo} bottles to go</P>
          </FlexBox>
        </FlexBox>
        <Gutter size={40} />
        <Button title="Summaries >>" style={{ paddingVertical: 5 }} onPress={() => { navigation.navigate('Summary') }} />
      </Center>
    </BaseLayout>
  );
});


export default WaterTrackingScreen;