import React, { useEffect } from 'react';
import { View, StyleSheet, Image } from 'react-native';
import Header from '../../shared/components/Header';
import BaseLayout from '../../shared/layouts/BaseLayout';
import DropDown from '../../shared/components/DropDown';
import Button from '../../shared/components/Button';
import { useNavigation } from '@react-navigation/native';
import Center from '../../shared/components/Center';
import CartridgeComponent from '../components/CartridgeComponent';
import Gutter from '../../shared/components/Gutter';
import FlexBox from '../../shared/components/FlexBox';
import Link from '../../shared/components/Link';
import H2 from '../../shared/components/typography/H2';
import P from '../../shared/components/typography/P';
import Cartridge from '../../../database/models/Cartridge';
import H3 from '../../shared/components/typography/H3';
import { observer } from 'mobx-react-lite';
import { scale } from 'react-native-size-matters';
import { useStores } from '../../../RootStoreContext';

const TabletTrackingScreen: React.FC = observer(() => {

  const [selectedMicro, setSelectedMicro] = React.useState('');
  const [cartridge, setCartridge] = React.useState<Cartridge | undefined>(undefined);
  const navigation = useNavigation();
  const { cartridgeStore } = useStores()

  useEffect(() => {
    const fetchCartridge = () => {
      const cartridgeId = navigation.getState()!.routes[1]?.params?.cartridgeId ?? null;
      console.log('cartridgeId', cartridgeId);
      if (cartridgeId) {
        const cartridge = cartridgeStore.cartridges.find(cartridge => cartridge.id === cartridgeId);
        console.log('cartridge', cartridgeStore.cartridges);
        if (cartridge) {
          setCartridge(cartridge);
        }
      }
    };
    fetchCartridge();
  }, [])

  const TrackingMicros = () => {
    return (
      <View style={{ borderWidth: 1.5, borderColor: '#000', padding: 8, marginTop: 15 }} >
        <P>Keep track of your micros</P>
        <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginTop: 15 }} >
          <DropDown options={Object.keys(cartridgeStore.groupedCartridges)} onSelect={(option) => setSelectedMicro(option)} selectedOption={selectedMicro} />
          <Button title="Summary >>" variant='outline' onPress={() => navigation.navigate('Summary')} style={{ paddingVertical: 5 }} textStyle={{ fontSize: 14 }} />
        </View>
      </View>
    );
  }

  return (
    <BaseLayout>
      <Header title="Tablet Tracking" />
      <TrackingMicros />
      <Gutter size={30} />
      {cartridge &&
        <Center>
          <CartridgeComponent balance={cartridge?.tablet_count || 0} />
          <Gutter size={20} />
          <H3>{cartridge?.name}</H3>
          <Gutter size={10} />
          <H2>0{cartridge?.tablet_count || 0} of 07</H2>
          <Link text='Take Dose' onPress={() => navigation.navigate('Home')} />
          <Gutter size={10} />
          <P>{cartridgeStore.groupedCartridges[cartridge?.name].length} Cartridges remaining</P>
          <FlexBox direction='row' >
            {cartridgeStore.groupedCartridges[cartridge?.name].map((cartridge: Cartridge, index: number) => {
              return (
                <Image source={require('../../../../assets/images/cartridge/active.png')} key={index} resizeMode="contain" style={{ width: scale(20), height: scale(40) }} />
              );
            })}
          </FlexBox>
          <FlexBox direction="row" justify="space-around" align="center" style={{ width: '100%', padding: 10 }} >
            <Link text="Edit Inventory" onPress={() => navigation.navigate('Inventory')} />
            <Gutter size={10} />
            <Button title="Stock up >>" variant='outline' onPress={() => navigation.navigate('Inventory')} style={{ paddingVertical: 5 }} />
          </FlexBox>
          <Gutter size={20} />
          <Button title="Summaries >>" onPress={() => navigation.navigate('Summary')} style={{ paddingVertical: 5 }} />
        </Center>
      }
    </BaseLayout>
  );
});

const styles = StyleSheet.create({

});

export default TabletTrackingScreen;