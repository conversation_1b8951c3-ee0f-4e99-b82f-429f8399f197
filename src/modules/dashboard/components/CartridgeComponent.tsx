import React from 'react';
import { View, Text } from 'react-native';
import { ScaledSheet } from 'react-native-size-matters';

interface CartridgeComponentProps {
  balance: number;
  width?: number;
  height?: number;
}

const CartridgeComponent: React.FC<CartridgeComponentProps> = ({ balance, width, height }) => {
  const renderCartridges = () => {
    let Cartridges = [];
    for (let i = 0; i < balance; i++) {
      Cartridges.push(<View key={i} style={styles.Cartridge} />);
    }
    return Cartridges;
  };

  return (
    <>
      <View style={styles.container}>
        <View style={[styles.box, {width: width || 130, height: height || 180}]}>
          {renderCartridges()}
        </View>
      </View>
      <Text style={styles.text}>Cartridge 01</Text>
    </>
  );
};

const styles = ScaledSheet.create({
  container: {
    borderWidth: 1,
    borderColor: 'black',
    padding: '10@s',
  },
  box: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: 'black',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'flex-end'
  },
  text: {
    fontSize: 18,
    marginTop: 10,
  },
  Cartridge: {
    width: '100%',
    height: 180/7,
    backgroundColor: '#e9b0af',
    borderWidth: 0.8,
    borderColor: 'black',
  },
});

export default CartridgeComponent;