import React from 'react';
import { Alert, Platform, View } from 'react-native';
import { scale, ScaledSheet } from 'react-native-size-matters';
import DropDown from '../../shared/components/DropDown';
import FlexBox from '../../shared/components/FlexBox';
import Button from '../../shared/components/Button';
import H3 from '../../shared/components/typography/H3';
import Gutter from '../../shared/components/Gutter';
import DateTimePicker from '@react-native-community/datetimepicker'
import { useStores } from '../../../RootStoreContext';
import { observer } from 'mobx-react-lite';
import { ANDROID_CHANNELS } from '../../shared/globals';

interface ReminderComponentProps {
  title: string
  message: string
  type: string
  close?: () => void
}

const ReminderComponent: React.FC<ReminderComponentProps> = observer(({ title, message, type, close }) => {

  const { reminderStore } = useStores()

  const [selectedOption, setSelectedOption] = React.useState<string>('Today');
  const [selectedTime, setSelectedTime] = React.useState<Date | null>(null);
  const [selectedRepeat, setSelectedRepeat] = React.useState<string>('');
  const [showPicker, setShowPicker] = React.useState<boolean>(false);

  // const showPicker = () => {
  //   DateTimePickerAndroid.open({
  //     value: new Date(),
  //     onChange: (event, selectedDate) => {
  //       const currentDate = selectedDate || new Date();
  //       setSelectedTime(currentDate.toLocaleTimeString());
  //     },
  //     mode: 'time',
  //     is24Hour: true,
  //   });
  // }

  return (
    <>
      <View style={styles.container}>
        <H3 style={{ color: 'white' }} >Schedule a Reminder</H3>
        <Gutter size={20} />
        <DropDown label='Select one' darkContent={false} options={['Today', 'Tomorrow']} selectedOption={selectedOption} onSelect={(option) => { setSelectedOption(option) }} />
        <Gutter size={20} />
        <FlexBox direction='row' align='center' justify='space-evenly' >
          <Button title={selectedTime ? selectedTime.toLocaleTimeString() : 'Select Time'} style={{ borderColor: 'white' }} textStyle={{ fontSize: scale(12) }} onPress={() => { setShowPicker(true) }} />
          <DropDown label='Repeat' options={['Yes', 'No']} selectedOption={selectedRepeat} onSelect={(option) => { setSelectedRepeat(option) }} darkContent={false} />
        </FlexBox>
        <Gutter size={20} />
        <Button title='Save' style={{ backgroundColor: 'white' }} textStyle={{ color: 'black' }} onPress={() => {
          console.log('Selected Option:', selectedOption);
          console.log('Selected Time:', selectedTime);
          console.log('Selected Repeat:', selectedRepeat);
          if (!selectedOption || !selectedTime || !selectedRepeat) {
            Alert.alert('Error', 'Please fill all the fields');
            return
          }

          const date = new Date(); // Start with current date/time

          if (selectedOption === 'Tomorrow') {
            date.setDate(date.getDate() + 1);
          }
          date.setHours(selectedTime.getHours(), selectedTime.getMinutes(), 0, 0);
          console.log('Scheduled DateTime:', date);
          reminderStore.scheduleReminder(
            title,
            message,
            date,
            selectedRepeat === 'Yes' ? 'day' : undefined,
            ANDROID_CHANNELS.nutrition.id,
            type
          );
          if (close) close();
        }} />
        {showPicker && (<DateTimePicker
          style={{ backgroundColor: 'white' }}
          testID="dateTimePicker"
          value={selectedTime ? new Date(new Date().toDateString() + ' ' + selectedTime) : new Date()}
          mode="time"
          is24Hour={true}
          display={Platform.OS === 'ios' ? 'spinner' : 'default'}
          onChange={(event, selectedDate) => {
            const currentDate = selectedDate || new Date();
            setSelectedTime(currentDate);
            setShowPicker(false);
          }}
        />)}
      </View>
    </>
  );
});

const styles = ScaledSheet.create({
  container: {
    padding: '10@s',
  }
});

export default ReminderComponent;