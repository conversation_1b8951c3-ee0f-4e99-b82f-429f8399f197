import React from 'react'
import { View, StyleSheet, FlatList } from 'react-native'
import { DayDoseStatus } from '../../../database/services/TabletTrackingService'
import P from '../../shared/components/typography/P'

interface Props {
  weekData: DayDoseStatus
}

const DoseWeekView: React.FC<Props> = ({ weekData }) => {
  return (
    <View style={styles.container}>
      <FlatList
        data={weekData.data}
        horizontal
        keyExtractor={(item) => item.date.toISOString()}
        renderItem={({ item }) => (
          <View
            style={[
              styles.dot,
              item.taken ? styles.filledDot : styles.disabledDot
            ]}
          />
        )}
      />
      <P>Today's Dose: <P style={{ fontWeight: 800 }} >{weekData.todayTaken ? 'Taken': 'Not taken'}</P></P>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'space-between',
  },
  dot: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 6,
    borderWidth: 1,
  },
  filledDot: {
    backgroundColor: '#fff',
  },
  disabledDot: {
    backgroundColor: '#fff',
    opacity: 0.5,
  },
})

export default DoseWeekView