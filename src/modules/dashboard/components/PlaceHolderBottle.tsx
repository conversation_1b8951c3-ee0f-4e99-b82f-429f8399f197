import React from 'react';
import { View, StyleSheet } from 'react-native';
import Button from '../../shared/components/Button';
import Gutter from '../../shared/components/Gutter';
import BluetoothStore from '../../shared/stores/BluetoothStore';


const PlaceHolderBottle = () => {

  return (
    <View style={styles.container}>
      <View style={[styles.compartment, styles.nostril]}>
      </View>

      <View style={[styles.compartment, styles.nutrition]}>
      </View>

      <View style={[styles.compartment, styles.hydration]}>
      </View>

      <View style={[styles.compartment, styles.uv]}>
      </View>
      <Gutter size={24} />
      <Button title='Battery' disabled={true} />
      <Gutter size={24} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 0.8,
    width: '70%',
    flexDirection: 'column',
    marginHorizontal: 'auto',
    marginTop: 20,
  },
  compartment: {
    borderWidth: 2,
    borderColor: 'black',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
  nostril: {
    flex: 0.2,
    backgroundColor: 'white',
    borderBottomWidth: 0,
    borderTopRightRadius: 50,
    borderTopLeftRadius: 50,
  },
  nutrition: {
    flex: 0.4,
    justifyContent: 'flex-end',
    alignItems: 'flex-start',
    padding: 10,
    borderBottomWidth: 0,
  },
  hydration: {
    flex: 0.4,
    justifyContent: 'flex-end',
    alignItems: 'flex-start',
    padding: 10,
    borderBottomWidth: 0,
  },
  uv: {
    flex: 0.1,
    justifyContent: 'flex-end',
    alignItems: 'flex-start',
    padding: 10,
    paddingBottom: 20,
    borderBottomRightRadius: 50,
    borderBottomLeftRadius: 50,
  }
});

export default PlaceHolderBottle;