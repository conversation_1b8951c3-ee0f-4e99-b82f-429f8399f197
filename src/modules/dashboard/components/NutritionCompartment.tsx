import React, { useEffect, useState } from 'react';
import { View, Image, StyleSheet, Alert } from 'react-native';
import Button from '../../shared/components/Button';
import { useNavigation } from '@react-navigation/native';
import P from '../../shared/components/typography/P';
import H4 from '../../shared/components/typography/H4';
import Gutter from '../../shared/components/Gutter';
import Cartridge from '../../../database/models/Cartridge';
import AlertPopover from '../../shared/components/AlertPopover';
import { DayDoseStatus, getTabletTrackingForWeek } from '../../../database/services/TabletTrackingService';
import DoseWeekView from './DoseWeekView';
import { useStores } from '../../../RootStoreContext';
import Link from '../../shared/components/Link';
import ReminderComponent from './ReminderComponent';
import { DISPENSE_COMMAND_ID, REMINDER_TYPES } from '../../shared/globals';
import BluetoothStore from '../../shared/stores/BluetoothStore';

interface NutritionCompartmentProps {
  cartridge: Cartridge // Define any props if needed
}

const NutritionCompartment: React.FC<NutritionCompartmentProps> = ({ cartridge }) => {

  const navigation = useNavigation()
  const [showPopup, setShowPopup] = React.useState(false);
  const [showReminder, setShowReminder] = React.useState(false);
  const { userStore } = useStores()
  const [dispenseLoading, setDispenseLoading] = useState(false)

  const [weekData, setWeekData] = useState<DayDoseStatus>({ data: [], todayTaken: false })

  useEffect(() => {
    if (cartridge?.id) {
      getTabletTrackingForWeek(userStore.user!.id, cartridge.id).then(setWeekData)
    }
  }, [cartridge?.id, showPopup])

  if (!cartridge) {
    return (
      <View style={styles.container}>
        <P>No cartridge found</P>
        <Link text='Add' onPress={() => {
          navigation.navigate('Inventory')
        }} />

        <Link text='Refresh' onPress={() => {
          userStore.loadUser()
        }} />
      </View>
    );
  }

  return (
    <>
      <View style={styles.container}>
        <View>
          <DoseWeekView weekData={weekData} />
        </View>
        <View>
          <H4 style={{ fontWeight: 800 }} onPress={() => { navigation.navigate('TabletTracking', { cartridgeId: cartridge?.id }) }} >Cartridge 01</H4>
          <P>{cartridge?.name}</P>
          <Gutter />
          <View style={{ display: 'flex', flexDirection: 'row', justifyContent: 'space-between' }} >
            <Button title={`Dispense ${cartridge?.name} Now`} onPress={() => { setShowPopup(true) }} small />
            <Gutter size={10} direction='horizontal' />
            <Button title={`Schedule ${cartridge?.name}`} onPress={() => { setShowReminder(true) }} small />
          </View>
        </View>
      </View>
      <AlertPopover image={<Image source={require('../../../../assets/images/water_placeholder.png')} resizeMode='contain' style={{ width: 200, height: 200 }} />} content={`Reminder: Make sure theres enough water! ${'\n'} ${weekData.todayTaken ? 'You have already taken your dose today, are you sure want to dispense again ?' : ''}`} visible={showPopup} onClose={() => { setShowPopup(false) }} buttons={[{
        text: dispenseLoading ? 'Dispensing now...' : 'Dispense now', disabled: dispenseLoading, onPress: async () => {
          try {
            if(cartridge.tablet_count <= 0) {
              Alert.alert('Cartridge empty, load new cartridge');
              return;
            }
            setDispenseLoading(true)
            await BluetoothStore.writeData(DISPENSE_COMMAND_ID, cartridge.id)
            const loged = await cartridge.logTabletIntake(cartridge.name)
            console.log('Tablet intake logged:', loged);
            setDispenseLoading(false)
            setShowPopup(false);
          } catch (error) {
            setDispenseLoading(false)
            setShowPopup(false);
            console.error('Error logging tablet intake:', error);
          }
        }
      }]} backgroundColor='#000' />
      <AlertPopover customContent={<ReminderComponent title={`Dispense ${cartridge.name}`} message={`Reminder to dispense ${cartridge.name} now`} type={REMINDER_TYPES.nutrition} close={() => { setShowReminder(false) }} />} backgroundColor='#000' visible={showReminder} onClose={() => { setShowReminder(false) }} />
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 10,
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'space-between',
  }
});

export default NutritionCompartment;