import React from 'react';
import { View, Text, StyleSheet, Image } from 'react-native';
import Center from '../../shared/components/Center';
import Button from '../../shared/components/Button';
import AlertPopover from '../../shared/components/AlertPopover';
import { useNavigation } from '@react-navigation/native';
import H2 from '../../shared/components/typography/H2';

const UVCompartment: React.FC = () => {

  const navigation = useNavigation();
  const [uvActive, setUvActive] = React.useState(false);

  return (
    <>
      <Center>
        <H2>UV</H2>
        <Button title='Start UV now' onPress={() => { setUvActive(true) }} small />
      </Center>
      <AlertPopover image={<Image source={require('../../../../assets/images/water_placeholder.png')} resizeMode='contain' style={{width:200, height: 200}} />} content='Reminder: Make sure theres enough water! Place the bottle on the stead surface, do not hold the bottle. And do not open the bottle for any reason while UV is functioning' visible={uvActive} onClose={() => { setUvActive(false) }} buttons={[{
        text: 'Start UV Now', onPress: () => {
          navigation.navigate('UVScreen');
        }
      }]} backgroundColor='#000' />
    </> 
  );
};

const styles = StyleSheet.create({

});

export default UVCompartment;