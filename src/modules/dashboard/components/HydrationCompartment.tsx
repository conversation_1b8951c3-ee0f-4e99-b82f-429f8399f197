import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert } from 'react-native';
import { AnimatedCircularProgress } from 'react-native-circular-progress';
import Center from '../../shared/components/Center';
import Button from '../../shared/components/Button';
import FeatherIcon from '@react-native-vector-icons/feather';
import { useNavigation } from '@react-navigation/native';
import P from '../../shared/components/typography/P';
import { useStores } from '../../../RootStoreContext';
import { observer } from 'mobx-react-lite';
import AlertPopover from '../../shared/components/AlertPopover';
import { REMINDER_TYPES } from '../../shared/globals';
import ReminderComponent from './ReminderComponent';

const HydrationCompartment: React.FC = observer(() => {

  const { userStore } = useStores();
  const navigation = useNavigation();
  const [showReminder, setShowReminder] = React.useState(false);

  return (
    <Center>
      <TouchableOpacity onPress={() => navigation.navigate('WaterTracking')} >
        <AnimatedCircularProgress
          size={100}
          width={10}
          fill={(userStore.totalWaterIntake / 3000) * 100}
          tintColor="#fff"
          backgroundColor="#c4f1f6"
          rotation={0}
        >
          {
            (fill) => (
              <>
                <P>
                  Consumed
                </P>
                <P>{userStore.totalWaterIntake / 1000}L of 3L</P>
              </>
            )
          }
        </AnimatedCircularProgress>
      </TouchableOpacity>
      <View style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', flexDirection: 'row' }} >
        <Button title='-' onPress={() => {
          Alert.alert('Message', 'Yet to be implemented')
        }} style={{ backgroundColor: 'transparent', borderColor: 'transparent' }} textStyle={{ fontSize: 24, fontWeight: 'bold', color: 'black' }} />
        <FeatherIcon name='droplet' size={24} color='white' />
        <Button title='+' onPress={() => {
          userStore.logWaterIntake(250);
        }} style={{ backgroundColor: 'transparent', borderColor: 'transparent' }} textStyle={{ fontSize: 24, fontWeight: 'bold', color: 'black' }} />
      </View>
      <Button title='Schedule Reminders' onPress={() => { setShowReminder(true) }} small />
      <AlertPopover customContent={<ReminderComponent title={`Hydration Reminder`} message={`Reminder to drink water`} type={REMINDER_TYPES.hydration} close={() => { setShowReminder(false) }} />} backgroundColor='#000' visible={showReminder} onClose={() => { setShowReminder(false) }} />
    </Center>
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  text: {
    fontSize: 18,
    color: '#333',
  },
});

export default HydrationCompartment;