export type ShareRequestPayload = {
  recipient_email: string;
};

export type RespondToRequestPayload = {
  action: 'accept' | 'reject';
  access_level?: 'water' | 'tablet' | 'both'; // required only for accept
};

export type ShareRequestResponse = {
  message: string;
};

export type RespondToRequestResponse = {
  message: string;
};

export type ShareRequestListResponse = {
  incoming: ShareRequestDTO[];
  outgoing: ShareRequestDTO[];
};

export type ShareRequestDTO = {
  id: number;
  status: string;
  requested_at: string;
  sender_email?: string;
  recipient_email?: string;
  bottle_id?: number; // optional, not required in UI for now
};

export type AccessPermissionResponse = {
  id: number;
  bottle_id: number;
  access_level: 'water' | 'tablet' | 'both';
  granted_by: string;
  granted_at: string;
};
