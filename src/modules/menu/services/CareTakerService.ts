import ApiClient from '../../../services/ApiClient';
import {
  ShareRequestPayload,
  ShareRequestResponse,
  ShareRequestListResponse,
  AccessPermissionResponse,
  RespondToRequestPayload,
  RespondToRequestResponse
} from '../types/CareTaker';

const CareTakerService = {
  sendShareRequest: async (data: ShareRequestPayload): Promise<ShareRequestResponse> => {
    const response = await ApiClient.post('/share_requests', data);
    return response.data;
  },
  fetchShareRequests: async (): Promise<ShareRequestListResponse> => {
    const response = await ApiClient.get('/share_requests');
    return response.data;
  },
  respondToRequest: async (
    requestId: number,
    data: RespondToRequestPayload
  ): Promise<RespondToRequestResponse> => {
    const response = await ApiClient.patch(`/share_requests/${requestId}/respond`, data);
    return response.data;
  },
  getAccessPermissions: async (): Promise<AccessPermissionResponse[]> => {
    const response = await ApiClient.get('/access_permissions');
    return response.data;
  },
};

export default CareTakerService;
