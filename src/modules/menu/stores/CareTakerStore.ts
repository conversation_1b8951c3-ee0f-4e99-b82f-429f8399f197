import { makeAutoObservable, runInAction } from 'mobx';
import { RootStore } from '../../shared/stores/RootStore';
import CareTakerService from '../services/CareTakerService';
import {
  ShareRequestDTO,
  AccessPermissionResponse
} from '../types/CareTaker';

export default class CareTakerStore {
  rootStore: RootStore;
  isLoading = false;

  incomingRequests: ShareRequestDTO[] = [];
  outgoingRequests: ShareRequestDTO[] = [];
  accessPermissions: AccessPermissionResponse[] = [];

  constructor(rootStore: RootStore) {
    this.rootStore = rootStore;
    makeAutoObservable(this);
  }

  async sendShareRequest(email: string) {
    this.isLoading = true;
    try {
      await CareTakerService.sendShareRequest({ recipient_email: email });
      await this.fetchShareRequests();
    } catch (error) {
      console.error('Failed to send share request:', error);
    } finally {
      runInAction(() => {
        this.isLoading = false;
      });
    }
  }

  async fetchShareRequests() {
    this.isLoading = true;
    try {
      const data = await CareTakerService.fetchShareRequests();
      console.log('Fetched share requests:', data);
      
      runInAction(() => {
        this.incomingRequests = data.incoming;
        this.outgoingRequests = data.outgoing;
      });
    } catch (error) {
      console.error('Failed to fetch share requests:', error);
    } finally {
      runInAction(() => {
        this.isLoading = false;
      });
    }
  }

  async respondToRequest(
    id: number,
    action: 'accept' | 'reject',
    accessLevel?: 'water' | 'tablet' | 'both'
  ) {
    this.isLoading = true;
    try {
      await CareTakerService.respondToRequest(id, { action, access_level: accessLevel });
      await this.fetchShareRequests();
      await this.fetchAccessPermissions();
    } catch (error) {
      console.error('Failed to respond to request:', error);
    } finally {
      runInAction(() => {
        this.isLoading = false;
      });
    }
  }

  async fetchAccessPermissions() {
    this.isLoading = true;
    try {
      const data = await CareTakerService.getAccessPermissions();
      runInAction(() => {
        this.accessPermissions = data;
      });
    } catch (error) {
      console.error('Failed to fetch access permissions:', error);
    } finally {
      runInAction(() => {
        this.isLoading = false;
      });
    }
  }
}
