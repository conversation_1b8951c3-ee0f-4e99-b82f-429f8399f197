import AsyncStorage from '@react-native-async-storage/async-storage';
import {makeAutoObservable, runInAction} from 'mobx';
import User from '../../../database/models/User';
import {findUserByEmail} from '../../../database/services/UserService';
import {RootStore} from '../../shared/stores/RootStore';
import {UserSearchResponse} from '../types/User';
import UserService from '../services/UserService';

export default class UserStore {
  rootStore: RootStore;
  user: User | null = null;
  isLoading: boolean = true;
  totalWaterIntake: number = 0;
  dayStreak: number = 0;
  userExists: boolean | null = null;

  constructor(rootStore: RootStore) {
    makeAutoObservable(this);
    this.rootStore = rootStore;
  }

  async loadUser() {
    try {
      this.isLoading = true;
      const userData = await AsyncStorage.getItem('user_email');
      console.log('User data from AsyncStorage:', userData);
      if (userData) {
        this.user = await findUserByEmail(userData);
        if (this.user) {
          const totalWaterIntake = await this.user.getWaterIntake();
          const dayStreak = await this.user.getWaterIntakeStreak();
          runInAction(() => {
            this.totalWaterIntake = totalWaterIntake;
            this.dayStreak = dayStreak;
          });
        }
        console.log('User loaded:', this.user.id);
      }
    } catch (error) {
      console.error('Error loading user data:', error);
    } finally {
      this.isLoading = false;
    }
  }

  async logWaterIntake(amount: number) {
    if (this.user) {
      try {
        const logged = await this.user.logWaterIntake(amount);
        const dayStreak = await this.user.getWaterIntakeStreak();
        console.log('Water intake logged:', logged);
        runInAction(() => {
          this.totalWaterIntake += amount;
          this.dayStreak = dayStreak;
        });
      } catch (error) {
        console.error('Error logging water intake:', error);
      }
    } else {
      console.error('User not found');
    }
  }

  async updateUser(data: Partial<User>) {
    const user = await this.user?.updateUser(data);
    console.log('User updated:', user?.first_name);

    runInAction(() => {
      this.user = user!;
    });
  }

  async checkIfUserExists(email: string) {
    this.isLoading = true;
    try {
      const exists = await UserService.checkIfExists(email);
      runInAction(() => {
        this.userExists = exists;
      });
    } catch (error) {
      runInAction(() => {
        this.userExists = null;
      });
    } finally {
      runInAction(() => {
        this.isLoading = false;
      });
    }
  }

  clearStatus() {
    this.userExists = null;
  }
}
