import React from "react";
import { View, Image, StyleSheet, ScrollView } from "react-native";
import Header from "../../shared/components/Header";
import Button from "../../shared/components/Button";
import AuthStore from "../../authentication/stores/AuthStore";
import H1 from "../../shared/components/typography/H1";
import P from "../../shared/components/typography/P";
import Gutter from "../../shared/components/Gutter";
import { observer } from "mobx-react-lite";
import { useNavigation } from "@react-navigation/native";
import { useStores } from "../../../RootStoreContext";

const ProfileScreen = observer(() => {

  const navigation = useNavigation()
  const { userStore } = useStores();
  const user = userStore.user;

  return (
    <View style={{flex: 1, padding: 12, backgroundColor:'white'}} >
    <Header title="Menu" />
    <ScrollView contentContainerStyle={styles.container}>
      <H1 >Profile</H1>
      <Gutter size={40} />
      <View style={styles.firstSection}>
        <Image
          source={{ uri: "https://t4.ftcdn.net/jpg/09/70/98/45/240_F_970984592_RRGgtz71ss7uXsCOlC21fxXCY4j8rGLL.jpg" }}
          style={styles.profileImage}
        />
        <View style={styles.profileDetails}>
          <P style={styles.label}>Name: {user?.first_name}</P>

          <P style={styles.label}>Email: {user?.email}</P>

          <P style={styles.label}>Phone: {user?.mobile}</P>
        </View>
      </View>
      <View style={styles.secondSection}>
        <P style={styles.label}>Age: 30</P>
        <P style={styles.label}>Gender: {user?.gender}</P>
        <P style={styles.label}>Location: AP</P>
        <P style={styles.label}>Height: {user?.height} cms</P>
        <P style={styles.label}>Weight: {user?.weight} kg</P>
        <P style={styles.label}>Health Status: Active</P>
      </View>
      <View style={{display: 'flex', flexDirection: 'row', justifyContent: 'space-between', marginTop: 20, padding: 16}} >
        <Button title='Edit Profile' variant="outline" onPress={() => navigation.navigate('EditProfile')} />
        <Button title='Logout' variant="outline" onPress={() => AuthStore.logout()} />
      </View>
    </ScrollView>
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: "#fff",
  },
  firstSection: {
    flexDirection: "row",
    marginBottom: 24,
    backgroundColor: "#ffffff",
    display: 'flex',
    justifyContent: 'space-between',
  },
  profileImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
    marginRight: 16,
  },
  profileDetails: {
    flex: 1,
    justifyContent: "center",
  },
  label: {
    fontSize: 16,
    color: "#000",
    padding: 5
  },
  secondSection: {
    backgroundColor: "#ffffff",
    padding: 16,
  },
});

export default ProfileScreen;
