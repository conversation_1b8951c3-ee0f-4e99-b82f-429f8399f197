import React from 'react';
import { View, Text, StyleSheet, SafeAreaView, FlatList } from 'react-native';
import { useStores } from '../../../RootStoreContext';
import Header from '../../shared/components/Header';
import { scale } from 'react-native-size-matters';

const ReminderScreen: React.FC = () => {

  const {reminderStore} = useStores()

  return (
    <SafeAreaView style={styles.container}>
      <FlatList 
        style={{ width: '100%' }}
        contentContainerStyle={{ padding: scale(15) }}
        data={reminderStore.reminders}
        keyExtractor={(item) => item.id.toString()}
        renderItem={({ item }) => {
          return (
            <View style={{ padding: 10, borderBottomWidth: 1, borderBottomColor: '#ccc' }}>
              <Text style={styles.text}>{item.description}</Text>
              <Text>{item.schedule}</Text>
            </View>
          );
        }}
        ListHeaderComponent={<Header title="Reminders" />}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  text: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
});

export default ReminderScreen;