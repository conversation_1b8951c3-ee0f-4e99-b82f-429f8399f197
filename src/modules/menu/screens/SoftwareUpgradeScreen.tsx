import React, {useState} from 'react';
import {Text, StyleSheet, Alert} from 'react-native';
import {observer} from 'mobx-react-lite';
import BaseLayout from '../../shared/layouts/BaseLayout';
import Button from '../../shared/components/Button';
import BluetoothStore from '../../shared/stores/BluetoothStore';
import {testFirmwareUpload} from '../../shared/utils';
import {SMP_CHARACTERISTIC_UUID, SMP_SERVICE_UUID} from '../../shared/globals';
import Header from '../../shared/components/Header';

const SoftwareUpgradeScreen = () => {
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState<number | null>(null);

  const handleUpgrade = async () => {
    if (!BluetoothStore.deviceId) {
      Alert.alert('Error', 'Device not connected');
      return;
    }

    try {
      setUploading(true);
      await testFirmwareUpload(
        BluetoothStore.deviceId,
        SMP_SERVICE_UUID,
        SMP_CHARACTERISTIC_UUID,
      );
      Alert.alert('Success', 'Test firmware chunk sent');
    } catch (error) {
      console.error('Firmware test failed:', error);
      Alert.alert('Error', 'Firmware test failed');
    } finally {
      setUploading(false);
    }
  };

  return (
    <BaseLayout>
      <Header title="Software Update" />
      <Text style={styles.label}>Firmware Test</Text>

      {progress !== null && (
        <Text style={styles.progress}>Progress: {progress}%</Text>
      )}

      <Button
        title={uploading ? 'Uploading...' : 'Send Test Chunk'}
        disabled={uploading}
        onPress={handleUpgrade}
        variant="outline"
        small
      />
    </BaseLayout>
  );
};

const styles = StyleSheet.create({
  label: {
    fontSize: 18,
    marginBottom: 12,
    marginTop: 20,
  },
  progress: {
    marginBottom: 12,
  },
});

export default observer(SoftwareUpgradeScreen);
