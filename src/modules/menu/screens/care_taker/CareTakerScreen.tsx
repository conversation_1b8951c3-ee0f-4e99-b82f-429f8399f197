import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  Alert,
} from 'react-native';
import CheckBox from '@react-native-community/checkbox';
import BaseLayout from '../../../shared/layouts/BaseLayout';
import Header from '../../../shared/components/Header';
import H2 from '../../../shared/components/typography/H2';
import Button from '../../../shared/components/Button';
import {observer} from 'mobx-react-lite';
import {useStores} from '../../../../RootStoreContext';

const CareTakerScreen = () => {
  const {userStore, careTakerStore} = useStores();
  const [email, setEmail] = useState('');
  const [selectedAccessLevel, setSelectedAccessLevel] = useState<
    'water' | 'tablet' | 'both' | null
  >(null);

  useEffect(()=>{
    careTakerStore.fetchShareRequests();
  }, [])

  const handleCheckEmail = () => {
    if (!email.includes('@')) {
      Alert.alert('Please enter a valid email');
      return;
    }
    userStore.checkIfUserExists(email);
  };

  const handleSendRequest = async () => {
    if (!userStore.userExists) {
      Alert.alert('Email does not exist');
      return;
    }
    await careTakerStore.sendShareRequest(email);
    Alert.alert('Request Sent');
    userStore.clearStatus();
    setEmail('');
  };

  const handleAcceptRequest = async (id: number) => {
    if (!selectedAccessLevel) {
      Alert.alert('Choose access level');
      return;
    }
    await careTakerStore.respondToRequest(id, 'accept', selectedAccessLevel);
  };

  const handleRejectRequest = async (id: number) => {
    await careTakerStore.respondToRequest(id, 'reject');
  };

  return (
    <BaseLayout>
      <Header title="Menu" />
      <H2>CareTaker</H2>

      <Text style={styles.sectionTitle}>Add a Bottle:</Text>
      <View style={styles.inputRow}>
        <TextInput
          placeholder="Email"
          value={email}
          onChangeText={text => {
            setEmail(text);
            userStore.clearStatus();
          }}
          style={styles.input}
        />
        <TouchableOpacity
          onPress={handleCheckEmail}
          style={styles.searchButton}>
          <Text style={styles.searchIcon}>🔍</Text>
        </TouchableOpacity>
      </View>

      {userStore.userExists === true && (
        <Text style={styles.successText}>✅ Email found</Text>
      )}
      {userStore.userExists === false && (
        <Text style={styles.errorText}>❌ Email not found</Text>
      )}

      <Button
        title="Send Request"
        small
        style={{alignSelf: 'flex-start', marginTop: 8}}
        variant="outline"
        onPress={handleSendRequest}
      />

      <Text style={styles.sectionTitle}>Review Requests:</Text>
      {careTakerStore.incomingRequests.map(item => (
        <View style={styles.bottleCard} key={item.id}>
          <Text>From: {item.sender_email}</Text>
          <Text>Status: {item.status}</Text>

          <View style={styles.checkboxRow}>
            <CheckBox
              value={selectedAccessLevel === 'water'}
              onValueChange={() => setSelectedAccessLevel('water')}
            />
            <Text style={styles.checkboxLabel}>Water</Text>

            <CheckBox
              value={selectedAccessLevel === 'tablet'}
              onValueChange={() => setSelectedAccessLevel('tablet')}
            />
            <Text style={styles.checkboxLabel}>Tablet</Text>

            <CheckBox
              value={selectedAccessLevel === 'both'}
              onValueChange={() => setSelectedAccessLevel('both')}
            />
            <Text style={styles.checkboxLabel}>Both</Text>
          </View>

          <View style={styles.bottleButtonRow}>
            <Button
              title="Accept"
              small
              onPress={() => handleAcceptRequest(item.id)}
            />
            <Button
              title="Reject"
              small
              onPress={() => handleRejectRequest(item.id)}
            />
          </View>
        </View>
      ))}

      <Text style={styles.sectionTitle}>Pending Sent Requests:</Text>
      {careTakerStore.outgoingRequests.map(item => (
        <View style={styles.bottleCard} key={item.id}>
          <Text>To: {item.recipient_email}</Text>
          <Text>Status: {item.status}</Text>
          <Text>
            Requested At: {new Date(item.requested_at).toLocaleString()}
          </Text>
        </View>
      ))}

      <Text style={styles.sectionTitle}>Sharing:</Text>
      {careTakerStore.accessPermissions.map(permission =>
        renderBottleCard(
          `Bottle ID: ${permission.bottle_id}`,
          `Access: ${permission.access_level}`,
          `Granted by: ${permission.granted_by}`,
        ),
      )}
    </BaseLayout>
  );
};

const renderBottleCard = (title: string, nutrition: string, water: string) => (
  <View style={styles.bottleCard}>
    <Text style={styles.bottleTitle}>{title}</Text>
    <Text style={styles.bottleText}>{nutrition}</Text>
    <Text style={styles.bottleText}>{water}</Text>
    <View style={styles.bottleButtonRow}>
      <Button small variant="outline" title="More Info" onPress={() => {}} />
      <Button small variant="outline" title="Get Notified" onPress={() => {}} />
      <Button small variant="outline" title="Edit Sharing" onPress={() => {}} />
    </View>
  </View>
);

const styles = StyleSheet.create({
  sectionTitle: {
    fontWeight: 'bold',
    fontSize: 18,
    marginTop: 24,
    marginBottom: 8,
  },
  inputRow: {
    flexDirection: 'row',
    marginBottom: 8,
    alignItems: 'center',
  },
  input: {
    flex: 1,
    borderWidth: 1,
    padding: 8,
    borderRadius: 4,
  },
  searchButton: {
    padding: 8,
    marginLeft: 4,
  },
  searchIcon: {
    fontSize: 18,
  },
  successText: {
    color: 'green',
    marginBottom: 4,
  },
  errorText: {
    color: 'red',
    marginBottom: 4,
  },
  checkboxRow: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  checkboxLabel: {
    marginRight: 16,
    marginLeft: 4,
    fontSize: 14,
  },
  bottleCard: {
    marginTop: 16,
    padding: 12,
    borderWidth: 1,
    borderRadius: 8,
  },
  bottleTitle: {
    fontWeight: 'bold',
    fontSize: 16,
    marginBottom: 4,
  },
  bottleText: {
    marginBottom: 4,
  },
  bottleButtonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
});

export default observer(CareTakerScreen);
