import React, { useEffect, useState } from 'react';
import { View, FlatList, Image, SafeAreaView } from 'react-native';
import Header from '../../shared/components/Header';
import H1 from '../../shared/components/typography/H1';
import Gutter from '../../shared/components/Gutter';
import DropDown from '../../shared/components/DropDown';
import Button from '../../shared/components/Button';
import AlertPopover from '../../shared/components/AlertPopover';
import H3 from '../../shared/components/typography/H3';
import { scale } from 'react-native-size-matters';
import { observer } from 'mobx-react-lite';
import Cartridge from '../../../database/models/Cartridge';
import { groupBy } from 'lodash';
import H4 from '../../shared/components/typography/H4';
import Link from '../../shared/components/Link';
import FlexBox from '../../shared/components/FlexBox';
import P from '../../shared/components/typography/P';
import { useStores } from '../../../RootStoreContext';

const InventoryScreen: React.FC = observer(() => {

  const [quantity, setQuantity] = useState('');
  const [cartridge, setCartridge] = useState('');
  const [showPopup, setShowPopup] = useState(false);
  const { userStore, cartridgeStore } = useStores();

  useEffect(() => {
    cartridgeStore.loadCartridges()
  }, []);

  const AddInventory = () => {
    return (
      <View style={{ padding: scale(20) }}>
        <H3>Add Inventory</H3>
        <Gutter size={15} ></Gutter>
        <DropDown label='Select Cartidge Type' options={['Melatonin', 'Vitamin C', 'AVC']} onSelect={(option) => setCartridge(option)} selectedOption={cartridge} />
        <Gutter size={15} ></Gutter>
        <DropDown label='Select Quantity' options={['1', '2', '3', '4', '5', '6']} onSelect={(option) => setQuantity(option)} selectedOption={String(quantity)} />
        <Gutter size={15} ></Gutter>
        <Button title='Add' onPress={async () => {
          if (cartridge && quantity) {
            await cartridgeStore.createCartridges(cartridge, Number(quantity));
            setShowPopup(false);
            setCartridge('');
            setQuantity('');
          } else {
            console.log('Please select cartridge type and quantity');
          }
        }} />
      </View>
    );
  }

  const renderCartridge = ({ item }: { item: [string, Cartridge[]] }) => {
    return (
      <View style={{ padding: scale(10) }}>
        <H4>{item[0]}</H4>
        <Gutter size={15} ></Gutter>
        <FlexBox direction='row' >
          {item[1].map((cartridge: Cartridge, index: number) => {
            return (
              <Image source={require('../../../../assets/images/cartridge/active.png')} key={`inventory_${index}`} resizeMode="contain" style={{ width: scale(20), height: scale(40) }} />
            );
          })}
        </FlexBox>
        <P>{item[1].length} Cartridges Remaining</P>
        <Link text='Edit Inventory' />
      </View>
    );
  }

  const renderCartridges = () => {
    return (
      <FlatList
        keyExtractor={(_, index) => index.toString()}
        data={Object.entries(groupBy(cartridgeStore.cartridges.filter((c: Cartridge) => !c.completed), (cartridge: Cartridge) => cartridge.name))}
        renderItem={renderCartridge}
        ListFooterComponentStyle={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          width: '100%',
        }}
        ListFooterComponent={
          <Link text='Add Inventory' onPress={() => { setShowPopup(true) }} />
        }
      />
    );
  }

  return (
    <SafeAreaView style={{
      flex: 1,
      backgroundColor: '#fff',
      padding: scale(20),
      paddingBottom: scale(40),
    }}>
      <Header title='Menu' />
      <H1>Inventory</H1>
      <Gutter size={30} />
      {renderCartridges()}
      <AlertPopover customContent={AddInventory()} visible={showPopup} onClose={() => { setShowPopup(false) }} backgroundColor='#fff' />
    </SafeAreaView>
  );
});


export default InventoryScreen;