import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, FlatList, Alert } from 'react-native';
import Header from '../../shared/components/Header';
import { useNavigation } from '@react-navigation/native';
import Button from '../../shared/components/Button';
import P from '../../shared/components/typography/P';

type MenuItem = {
  id: string;
  label: string;
  onPress: () => void;
};

const MenuScreen: React.FC = () => {

  const navigation = useNavigation();

  const menuItems: MenuItem[] = [
    { id: '1', label: 'Profile', onPress: () => navigateTo('Profile') },
    { id: '2', label: 'Inventory', onPress: () => navigateTo('Inventory') },
    { id: '3', label: 'Caretaker', onPress: () => navigateTo('CareTaker') },
    { id: '4', label: 'Software Upgrade', onPress: () => navigateTo('SoftwareUpgrade') },
    { id: '5', label: 'Display Mode', onPress: () => navigateTo('DisplayMode') },
    { id: '6', label: 'Bluetooth', onPress: () => navigateTo('Bluetooth') },
    { id: '7', label: 'Privacy', onPress: () => navigateTo('Privacy') },
    { id: '8', label: 'Your Orders', onPress: () => navigateTo('Orders') },
    { id: '9', label: 'Help', onPress: () => navigateTo('Help') },
    { id: '10', label: 'Reminders', onPress: () => navigateTo('Reminder') },
  ];

  const navigateTo = (screen: string) => {
    navigation.navigate(screen);
  };

  const renderItem = ({ item }: { item: MenuItem }) => (
    <TouchableOpacity style={styles.menuItem} onPress={item.onPress}>
      <Text style={styles.bullet}>•</Text>
      <P style={styles.menuText}>{item.label}</P>
    </TouchableOpacity>
  );

  const renderFooter = () => {
    return (
      <View style={{display: 'flex', flexDirection: 'row', justifyContent: 'space-between', marginTop: 20, padding: 16}} >
        <Button title='Cleaning' onPress={() => Alert.alert('Logout')} />
        <Button title='Sync' onPress={() => Alert.alert('Delete Account')} />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <FlatList
        data={menuItems}
        renderItem={renderItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.list}
        ListHeaderComponent={<Header title='Menu' />}
        ListFooterComponent={renderFooter}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    padding: 16,
  },
  list: {
    paddingVertical: 8,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 5,
    paddingHorizontal: 16,
  },
  bullet: {
    fontSize: 30,
    color: '#000',
    marginRight: 16,
  },
  menuText: {
    fontSize: 18,
    color: '#000',
    fontWeight: '500',
  },
});

export default MenuScreen;
