import React, { useState } from "react";
import { View, StyleSheet, TextInput, ScrollView, Alert } from "react-native";
import Header from "../../shared/components/Header";
import Button from "../../shared/components/Button";
import P from "../../shared/components/typography/P";
import { observer } from "mobx-react-lite";
import { useStores } from "../../../RootStoreContext";

const EditProfileScreen = observer(() => {
  const { userStore } = useStores();
  const user = userStore.user;

  const [firstName, setFirstName] = useState(user?.first_name || "");
  const [lastName, setLastName] = useState(user?.last_name || "");
  const [email, setEmail] = useState(user?.email || "");
  const [mobile, setMobile] = useState(user?.mobile || "");
  const [gender, setGender] = useState(user?.gender || "");
  const [height, setHeight] = useState(user?.height?.toString() || "");
  const [weight, setWeight] = useState(user?.weight?.toString() || "");

  const handleSave = async () => {
    await userStore.updateUser({
      first_name: firstName,
      last_name: lastName,
      email: email,
      mobile: mobile,
      gender: gender,
      height: parseInt(height),
      weight: parseInt(weight),
    });
    Alert.alert("Profile Updated", "Your profile has been updated successfully.");
  };

  return (
    <View style={{ flex: 1, backgroundColor: "white", padding: 12 }}>
      <Header title="Edit Profile" />
      <ScrollView contentContainerStyle={styles.container}>
        <Input label="First Name" value={firstName} onChangeText={setFirstName} />
        <Input label="Last Name" value={lastName} onChangeText={setLastName} />
        <Input label="Email" value={email} onChangeText={setEmail} />
        <Input label="Phone" value={mobile} onChangeText={setMobile} />
        <Input label="Gender" value={gender} onChangeText={setGender} />
        <Input label="Height (cms)" value={height} onChangeText={setHeight} keyboardType="numeric" />
        <Input label="Weight (kg)" value={weight} onChangeText={setWeight} keyboardType="numeric" />

        <View style={{ marginTop: 30 }}>
          <Button title="Save" onPress={handleSave} />
        </View>
      </ScrollView>
    </View>
  );
});

const Input = ({
  label,
  value,
  onChangeText,
  keyboardType = "default",
}: {
  label: string;
  value: string;
  onChangeText: (val: string) => void;
  keyboardType?: "default" | "numeric" | "email-address";
}) => (
  <View style={{ marginBottom: 20 }}>
    <P style={styles.label}>{label}</P>
    <TextInput
      style={styles.input}
      value={value}
      onChangeText={onChangeText}
      keyboardType={keyboardType}
      placeholder={label}
    />
  </View>
);

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: "#fff",
  },
  label: {
    fontSize: 16,
    color: "#000",
    marginBottom: 4,
  },
  input: {
    borderWidth: 1,
    borderColor: "#ccc",
    padding: 10,
    borderRadius: 8,
    fontSize: 16,
  },
});

export default EditProfileScreen;
