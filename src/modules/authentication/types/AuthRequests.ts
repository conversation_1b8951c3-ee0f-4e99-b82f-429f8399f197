export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegistrationRequest {
  first_name: string;
  last_name: string;
  email: string;
  password: string;
  password_confirmation: string;
  provider?: 'google' | 'facebook' | 'apple';
  oauth_token?: string;
}

export interface ForgotPasswordRequest {
  email: string;
}

export interface ResetPasswordRequest {
  token: string; // Token sent via email
  password: string;
  password_confirmation: string;
}

export interface MoreInfoRequest {
  date_of_birth: string;
  city: string;
  country: string;
  height: number;
  weight: number
  gender: string;
}

