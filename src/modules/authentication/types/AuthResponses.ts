export interface LoginResponse {
  user: {
    id: number;
    name: string;
    email: string;
    created_at: string;
    updated_at: string;
  };
  token: string;
}

export interface RegistrationResponse {
  user: {
    id: number;
    name: string;
    email: string;
    first_name: string;
    last_name: string;
    created_at: string;
    updated_at: string;
  };
  token: string;
}

export interface UpdateResponse {
  message: string
}

export interface ForgotPasswordResponse extends UpdateResponse { }

export interface ResetPasswordResponse extends UpdateResponse { }

export interface MoreInfoResponse extends UpdateResponse { }