import React, { useState } from 'react';
import { View, TextInput, StyleSheet } from 'react-native';
import { observer } from 'mobx-react-lite';
import AuthStore from '../stores/AuthStore';
import { useNavigation } from '@react-navigation/native';
import Button from '../../shared/components/Button';
import Link from '../../shared/components/Link';
import Gutter from '../../shared/components/Gutter';
import H2 from '../../shared/components/typography/H2';
import { inputStyle } from '../../shared/styles';

const LoginScreen = observer(() => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const navigation = useNavigation()

  const handleLogin = async () => {
    await AuthStore.login({ email, password });
  };

  return (
    <View style={styles.container}>
      <H2>Login</H2>
      <Gutter size={30} />
      <TextInput
        placeholderTextColor={'#000'}
        style={inputStyle}
        placeholder="Email"
        value={email}
        onChangeText={setEmail}
        keyboardType="email-address"
        autoCapitalize="none"
      />
      <TextInput
        placeholderTextColor={'#000'}
        style={inputStyle}
        placeholder="Password"
        value={password}
        onChangeText={setPassword}
        secureTextEntry
      />
      <View style={{ display: 'flex', flexDirection: 'row', justifyContent: 'space-between', width: '100%', alignItems: 'center' }} >
        <Link text='Forgot password?' onPress={()=>{
          navigation.navigate('ForgotPassword')
        }} />
        <Button title="Login" onPress={handleLogin} variant='outline' />
        <Button title="Register" onPress={() => { navigation.navigate('Register') }} variant='outline' />
      </View>
    </View>
  );
});

const styles = StyleSheet.create({
  container: { flex: 1, padding: 20, display: 'flex', justifyContent: 'center', alignItems: 'center', backgroundColor: '#fff' },
  title: { fontSize: 24, fontWeight: 'bold', marginBottom: 20, textAlign: 'center' },
});

export default LoginScreen;
