import React, {useState} from 'react';
import {observer} from 'mobx-react-lite';
import {View, Text, TextInput, StyleSheet, Alert} from 'react-native';
import AuthStore from '../stores/AuthStore';
import {MoreInfoRequest} from '../types/AuthRequests';
import {useNavigation} from '@react-navigation/native';
import Button from '../../shared/components/Button';
import {inputStyle} from '../../shared/styles';

const MoreInfoScreen = observer(() => {
  const navigation = useNavigation();

  const [form, setForm] = useState({
    date_of_birth: '',
    city: '',
    gender: '',
    country: '',
    height: 0,
    weight: 0,
  });

  const handleInputChange = (key: string, value: string) => {
    setForm({...form, [key]: value});
  };

  const handleSubmit = async () => {
    if (
      !form.date_of_birth ||
      !form.city ||
      !form.gender ||
      !form.country ||
      !form.height ||
      !form.weight
    ) {
      Alert.alert('Error', 'All fields are required.');
      return;
    }
    const result = await AuthStore.update(form as MoreInfoRequest);
    if (result) {
      Alert.alert('Success', 'User information updated successfully.');
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Provide More Information</Text>

      <TextInput
        placeholderTextColor={'#000'}
        placeholder="Date of Birth (YYYY-MM-DD)"
        value={form.date_of_birth}
        onChangeText={text => handleInputChange('date_of_birth', text)}
        style={inputStyle}
      />
      <TextInput
        placeholderTextColor={'#000'}
        placeholder="City"
        value={form.city}
        onChangeText={text => handleInputChange('city', text)}
        style={inputStyle}
      />
      <TextInput
        placeholderTextColor={'#000'}
        placeholder="Gender (Male/Female/Other)"
        value={form.gender}
        onChangeText={text => handleInputChange('gender', text)}
        style={inputStyle}
      />
      <TextInput
        placeholderTextColor={'#000'}
        placeholder="Country"
        value={form.country}
        onChangeText={text => handleInputChange('country', text)}
        style={inputStyle}
      />
      <TextInput
        placeholderTextColor={'#000'}
        placeholder="Height (in cm)"
        value={form.height > 0 ? String(form.height) : ''}
        onChangeText={text => handleInputChange('height', text)}
        style={inputStyle}
        keyboardType="numeric"
      />
      <TextInput
        placeholderTextColor={'#000'}
        placeholder="Weight (in kg)"
        value={form.height > 0 ? String(form.weight) : ''}
        onChangeText={text => handleInputChange('weight', text)}
        style={inputStyle}
        keyboardType="numeric"
      />

      <Button title="Save" onPress={handleSubmit} />
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
});

export default MoreInfoScreen;
