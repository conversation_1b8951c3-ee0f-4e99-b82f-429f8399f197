import React, { useState } from 'react';
import { observer } from 'mobx-react-lite';
import { View, Text, TextInput, StyleSheet, Alert, TouchableOpacity } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import AuthStore from '../stores/AuthStore';
import { RegistrationRequest } from '../types/AuthRequests';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Button from '../../shared/components/Button';
import H2 from '../../shared/components/typography/H2';
import Gutter from '../../shared/components/Gutter';
import { inputStyle } from '../../shared/styles';

const RegisterScreen = observer(() => {

  const navigation = useNavigation();

  const [form, setForm] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    termsAccepted: false,
  });

  const handleInputChange = (key: string, value: string | boolean) => {
    setForm({ ...form, [key]: value });
  };

  const handleSubmit = async () => {
    console.log(AsyncStorage.getItem('user'));

    if (!form.termsAccepted) {
      Alert.alert('Error', 'You must accept the terms and conditions.');
      return;
    }
    if (form.password !== form.confirmPassword) {
      Alert.alert('Error', 'Passwords do not match.');
      return;
    }
    const dto: RegistrationRequest = {
      first_name: form.firstName,
      last_name: form.lastName,
      email: form.email,
      password: form.password,
      password_confirmation: form.confirmPassword
    }
    const result = await AuthStore.register(dto);
    if (result) {
      navigation.navigate('MoreInfo');
    }
  };

  return (
    <View style={styles.container}>
      <H2>Register</H2>
      <Gutter size={30} />
      <TextInput
        placeholder="First Name"
        placeholderTextColor={'#000'}
        value={form.firstName}
        onChangeText={(text) => handleInputChange('firstName', text)}
        style={inputStyle}
      />
      <TextInput
        placeholder="Last Name"
        placeholderTextColor={'#000'}
        value={form.lastName}
        onChangeText={(text) => handleInputChange('lastName', text)}
        style={inputStyle}
      />
      <TextInput
        placeholder="Email"
        placeholderTextColor={'#000'}
        value={form.email}
        onChangeText={(text) => handleInputChange('email', text)}
        style={inputStyle}
        keyboardType="email-address"
        autoCapitalize="none"
      />
      <TextInput
        placeholder="Password"
        placeholderTextColor={'#000'}
        value={form.password}
        onChangeText={(text) => handleInputChange('password', text)}
        style={inputStyle}
        secureTextEntry
        textContentType={'password'}
      />
      <TextInput
        placeholder="Confirm Password"
        placeholderTextColor={'#000'}
        value={form.confirmPassword}
        onChangeText={(text) => handleInputChange('confirmPassword', text)}
        style={inputStyle}
        secureTextEntry
        textContentType={'password'}
      />

      <View style={styles.checkboxContainer}>
        <TouchableOpacity
          onPress={() => handleInputChange('termsAccepted', !form.termsAccepted)}
          style={[
            styles.checkbox,
            { backgroundColor: form.termsAccepted ? '#000' : '#fff' },
          ]}
        />
        <Text style={styles.checkboxLabel}>I agree to the terms and conditions</Text>
      </View>

      <Button title="Create Account & Verify" onPress={handleSubmit} />
      <View style={{paddingTop: 25}} >
        <Text style={{ fontSize: 16, color: 'black', fontWeight: 500, textDecorationLine: 'underline', marginBottom: 20 }} >Already have an account?</Text>
        <Button title="Login" onPress={() => { navigation.navigate('Login') }} />
      </View>
    </View>
  );
});

const styles = StyleSheet.create({
  container: { flex: 1, padding: 20, display: 'flex', justifyContent: 'center', alignItems: 'center', backgroundColor: '#fff' },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 1,
    borderColor: '#000',
    marginRight: 10,
  },
  checkboxLabel: {
    fontSize: 14,
  },
});

export default RegisterScreen;
