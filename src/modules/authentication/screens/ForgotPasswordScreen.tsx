import React from 'react';
import { View, Text, TextInput, StyleSheet } from 'react-native';
import BaseLayout from '../../shared/layouts/BaseLayout';
import H2 from '../../shared/components/typography/H2';
import Button from '../../shared/components/Button';
import Header from '../../shared/components/Header';
import Gutter from '../../shared/components/Gutter';

const ForgotPasswordScreen: React.FC = () => {
  const [email, setEmail] = React.useState('');

  const handleForgotPassword = () => {
    console.log('Forgot password for:', email);
  };

  return (
    <BaseLayout>
      <Header title="" />
      <Gutter size={20} />
      <H2>Forgot Password</H2>
      <Gutter size={20} />
      <TextInput
        style={styles.input}
        placeholder="Enter your email"
        value={email}
        onChangeText={setEmail}
        keyboardType="email-address"
        autoCapitalize="none"
      />
      <Button title="Submit" onPress={handleForgotPassword} />
    </BaseLayout>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    padding: 16,
  },
  title: {
    fontSize: 24,
    marginBottom: 16,
    textAlign: 'center',
  },
  input: {
    height: 40,
    borderColor: 'gray',
    borderWidth: 1,
    marginBottom: 16,
    paddingHorizontal: 8,
  },
});

export default ForgotPasswordScreen;