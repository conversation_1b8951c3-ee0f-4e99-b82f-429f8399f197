import {makeAutoObservable, runInAction} from 'mobx';
import AuthService from '../services/AuthService';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  LoginRequest,
  MoreInfoRequest,
  RegistrationRequest,
} from '../types/AuthRequests';
import {Alert} from 'react-native';
import User from '../../../database/models/User';
import {
  createUser,
  findUserByEmail,
  updateUser,
} from '../../../database/services/UserService';

class AuthStore {
  isLoading = false;
  token: string | null = null;
  isAuthenticated = false;
  user: User | null = null;

  constructor() {
    makeAutoObservable(this);
  }

  async login(data: LoginRequest) {
    runInAction(() => {
      this.isLoading = true;
    });
    try {
      const response = await AuthService.login(data);
      const user = await findUserByEmail(data.email);
      console.log('User found:', user);
      if (user) {
        await AsyncStorage.setItem('token', response.token);
        await AsyncStorage.setItem('user_email', user.email);
        runInAction(() => {
          this.user = user;
          this.isAuthenticated = true;
          this.token = response.token;
        });
      } else {
        Alert.alert('Error', 'User not found');
      }
    } catch (error: any) {
      Alert.alert('Error', error.message);
    } finally {
      runInAction(() => {
        this.isLoading = false;
      });
    }
  }

  async register(data: RegistrationRequest) {
    try {
      const response = await AuthService.register(data);
      await AsyncStorage.setItem('user', JSON.stringify(response.user));
      const user = await createUser({
        first_name: response.user.first_name,
        last_name: response.user.last_name,
        email: response.user.email,
        token: response.token,
        gender: '',
        mobile: '',
        height: 0,
        weight: 0,
      });
      console.log('User created:', user);
      await AsyncStorage.setItem('token', response.token);
      await AsyncStorage.setItem('user_email', user.email);
      runInAction(() => {
        this.user = user;
      });
      return true;
    } catch (error: any) {
      console.log(error.message);
      Alert.alert('Error', error.message);
    }
  }

  async update(data: MoreInfoRequest) {
    try {
      const response = await AuthService.updateUserInfo(data);
      await updateUser({
        id: this.user!.id,
        gender: data.gender,
        height: parseInt(String(data.height)),
        weight: parseInt(String(data.weight)),
      });
      console.log('User updated:', this.user);
      return true;
    } catch (error: any) {
      Alert.alert('Error', error.message);
    } finally {
      runInAction(() => {
        this.isAuthenticated = true;
      });
    }
  }

  async logout() {
    runInAction(() => {
      this.isAuthenticated = false;
      this.token = null;
    });
    await AsyncStorage.removeItem('token');
    await AsyncStorage.removeItem('user');
    await AsyncStorage.removeItem('use_email');
  }

  async checkToken() {
    try {
      runInAction(() => {
        this.isLoading = true;
      });
      const token = await AsyncStorage.getItem('token');
      runInAction(() => {
        this.isAuthenticated = !!token;
        this.token = token;
      });
    } catch (error) {
      Alert.alert('Error', 'Failed to check authentication status.');
    } finally {
      runInAction(() => {
        this.isLoading = false;
      });
    }
  }
}

export default new AuthStore();
