

import ApiClient from '../../../services/ApiClient';
import { LoginRequest, MoreInfoRequest, RegistrationRequest } from '../types/AuthRequests';
import { LoginResponse, MoreInfoResponse, RegistrationResponse } from '../types/AuthResponses';


const AuthService = {
  login: async (data: LoginRequest): Promise<LoginResponse> => {
    const response = await ApiClient.post('/login', data);
    return response.data;
  },
  register: async (data: RegistrationRequest): Promise<RegistrationResponse> => {
    const response = await ApiClient.post('/register', data);
    return response.data;
  },
  updateUserInfo: async (data: MoreInfoRequest): Promise<MoreInfoResponse> => {
    const response = await ApiClient.put('/users', data);
    return response.data;
  },
};

export default AuthService;
