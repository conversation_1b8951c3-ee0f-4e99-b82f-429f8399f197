import React, { useEffect } from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import HomeScreen from '../modules/dashboard/screens/HomeScreen';
import MenuScreen from '../modules/menu/screens/MenuScreen';
import ProfileScreen from '../modules/menu/screens/ProfileScreen';
import UVScreen from '../modules/dashboard/screens/UVScreen';
import TabletTrackingScreen from '../modules/dashboard/screens/TabletTrackingScreen';
import SummaryScreen from '../modules/dashboard/screens/SummaryScreen';
import WaterTrackingScreen from '../modules/dashboard/screens/WaterTrackingScreen';
import InventoryScreen from '../modules/menu/screens/InventoryScreen';
import SoftwareUpgradeScreen from '../modules/menu/screens/SoftwareUpgradeScreen';
import EditProfileScreen from '../modules/menu/screens/EditProfileScreen';
import { useStores } from '../RootStoreContext';
import BluetoothStore from '../modules/shared/stores/BluetoothStore';
import BluetoothLoadingScreen from '../modules/shared/screens/BluetoothLoadingScreen';
import Loader from '../modules/shared/components/Loader';
import BluetoothLog from '../modules/shared/screens/BluetoothLogScreen';
import ReminderScreen from '../modules/menu/screens/ReminderScreen';
import CareTakerScreen from '../modules/menu/screens/care_taker/CareTakerScreen';

const Stack = createNativeStackNavigator();

const HomeNavigator = () => {

  const [ready, setReady] = React.useState(false);
  const rootStore = useStores();

  useEffect(() => {
    rootStore.initialize().then(() => {
      setReady(true);
    }
    ).catch((error) => {
      console.error('Error initializing root store:', error);
    }
    );
  }, [])

  if (!ready) {
    return <Loader />
  }

  return (
    <Stack.Navigator screenOptions={{ headerShown: false }} >
      <Stack.Screen name="Home" component={HomeScreen} />
      <Stack.Screen name="Menu" component={MenuScreen} />
      <Stack.Screen name="Profile" component={ProfileScreen} />
      <Stack.Screen name="UVScreen" component={UVScreen} />
      <Stack.Screen name="TabletTracking" component={TabletTrackingScreen} />
      <Stack.Screen name="WaterTracking" component={WaterTrackingScreen} />
      <Stack.Screen name="Summary" component={SummaryScreen} />
      <Stack.Screen name="Inventory" component={InventoryScreen} />
      <Stack.Screen name="SoftwareUpgrade" component={SoftwareUpgradeScreen} />
      <Stack.Screen name="EditProfile" component={EditProfileScreen} />
      <Stack.Screen name="BluetoothLog" component={BluetoothLog} />
      <Stack.Screen name="Reminder" component={ReminderScreen} />
      <Stack.Screen name="CareTaker" component={CareTakerScreen} />
    </Stack.Navigator>
  );
};

export default HomeNavigator;
