import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import LoginScreen from '../modules/authentication/screens/LoginScreen';
import RegisterScreen from '../modules/authentication/screens/RegisterScreen';
import MoreInfoScreen from '../modules/authentication/screens/MoreInfoScreen';
import ForgotPasswordScreen from '../modules/authentication/screens/ForgotPasswordScreen';


const Stack = createNativeStackNavigator();

const AuthNavigator = () => {
  return (
    <Stack.Navigator initialRouteName="Login" screenOptions={{headerShown: false}} >
      <Stack.Screen name="Login" component={LoginScreen} />
      <Stack.Screen name="Register" component={RegisterScreen} />
      <Stack.Screen name="MoreInfo" component={MoreInfoScreen} />
      <Stack.Screen name="ForgotPassword" component={ForgotPasswordScreen} />
    </Stack.Navigator>
  );
};

export default AuthNavigator;
