import React, { useEffect, useState } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import AuthNavigator from './AuthNavigator';
import HomeNavigator from './HomeNavigator';
import AuthStore from '../modules/authentication/stores/AuthStore';
import Loader from '../modules/shared/components/Loader';
import { observer } from 'mobx-react-lite';
import InitializerService from '../services/InitializerService';

const AppNavigator = observer(() => {

  const initializer = InitializerService.getInstance()
  initializer.init();

  useEffect(() => {
    AuthStore.checkToken()
  }, []);

  if (AuthStore.isLoading) {
    return <Loader />;
  }

  return (
    <NavigationContainer>
      {AuthStore.isAuthenticated ? <HomeNavigator /> : <AuthNavigator />}
    </NavigationContainer>
  );
});

export default AppNavigator;
