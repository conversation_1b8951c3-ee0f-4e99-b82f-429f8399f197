class CreateAccessPermissions < ActiveRecord::Migration[7.0]
  def change
    create_table :access_permissions do |t|
      t.references :bottle, null: false, foreign_key: true
      t.references :granted_to_user, null: false, foreign_key: { to_table: :users }
      t.references :granted_by_user, null: false, foreign_key: { to_table: :users }
      t.string :access_level
      t.datetime :granted_at
      t.datetime :expires_at
      t.datetime :revoked_at

      t.timestamps
    end
  end
end
