class CreateShareRequests < ActiveRecord::Migration[7.0]
  def change
    create_table :share_requests do |t|
      t.references :sender, null: false, foreign_key: { to_table: :users }
      t.references :recipient, null: false, foreign_key: { to_table: :users }
      t.references :bottle, null: false, foreign_key: true
      t.string :status
      t.datetime :requested_at
      t.datetime :responded_at

      t.timestamps
    end
  end
end
