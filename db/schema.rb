# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source <PERSON>s uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[7.0].define(version: 2025_06_07_073506) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "plpgsql"

  create_table "access_permissions", force: :cascade do |t|
    t.bigint "bottle_id", null: false
    t.bigint "granted_to_user_id", null: false
    t.bigint "granted_by_user_id", null: false
    t.string "access_level"
    t.datetime "granted_at"
    t.datetime "expires_at"
    t.datetime "revoked_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["bottle_id"], name: "index_access_permissions_on_bottle_id"
    t.index ["granted_by_user_id"], name: "index_access_permissions_on_granted_by_user_id"
    t.index ["granted_to_user_id"], name: "index_access_permissions_on_granted_to_user_id"
  end

  create_table "bottles", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_bottles_on_user_id"
  end

  create_table "cartridges", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.string "name"
    t.integer "tablet_count"
    t.boolean "installed"
    t.boolean "completed"
    t.string "uuid"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_cartridges_on_user_id"
  end

  create_table "reminders", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.string "reminder_type", null: false
    t.string "schedule", null: false
    t.string "status", null: false
    t.string "description"
    t.integer "notification_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_reminders_on_user_id"
  end

  create_table "share_requests", force: :cascade do |t|
    t.bigint "sender_id", null: false
    t.bigint "recipient_id", null: false
    t.bigint "bottle_id", null: false
    t.string "status"
    t.datetime "requested_at"
    t.datetime "responded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "access_level"
    t.index ["bottle_id"], name: "index_share_requests_on_bottle_id"
    t.index ["recipient_id"], name: "index_share_requests_on_recipient_id"
    t.index ["sender_id"], name: "index_share_requests_on_sender_id"
  end

  create_table "tablet_trackings", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "cartridge_id", null: false
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["cartridge_id"], name: "index_tablet_trackings_on_cartridge_id"
    t.index ["user_id"], name: "index_tablet_trackings_on_user_id"
  end

  create_table "users", force: :cascade do |t|
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "first_name", null: false
    t.string "last_name", null: false
    t.datetime "terms_accepted_at"
    t.integer "sign_in_count", default: 0, null: false
    t.datetime "current_sign_in_at"
    t.datetime "last_sign_in_at"
    t.string "current_sign_in_ip"
    t.string "last_sign_in_ip"
    t.string "provider"
    t.string "uid"
    t.string "avatar_url"
    t.string "confirmation_token"
    t.datetime "confirmed_at"
    t.datetime "confirmation_sent_at"
    t.date "date_of_birth"
    t.string "gender"
    t.string "city"
    t.string "country"
    t.decimal "height"
    t.decimal "weight"
    t.index ["confirmation_token"], name: "index_users_on_confirmation_token", unique: true
    t.index ["email"], name: "index_users_on_email", unique: true
    t.index ["reset_password_token"], name: "index_users_on_reset_password_token", unique: true
  end

  create_table "water_trackings", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.integer "consumed_ml"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_water_trackings_on_user_id"
  end

  add_foreign_key "access_permissions", "bottles"
  add_foreign_key "access_permissions", "users", column: "granted_by_user_id"
  add_foreign_key "access_permissions", "users", column: "granted_to_user_id"
  add_foreign_key "bottles", "users"
  add_foreign_key "cartridges", "users"
  add_foreign_key "reminders", "users"
  add_foreign_key "share_requests", "bottles"
  add_foreign_key "share_requests", "users", column: "recipient_id"
  add_foreign_key "share_requests", "users", column: "sender_id"
  add_foreign_key "tablet_trackings", "cartridges"
  add_foreign_key "tablet_trackings", "users"
  add_foreign_key "water_trackings", "users"
end
